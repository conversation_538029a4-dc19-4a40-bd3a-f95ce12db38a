"use client"
import { AuthContextProvider } from "../../../context/AuthContextProvider";
import React, { useEffect, useState } from 'react';
import { Filter, Search, BookOpen, Users } from 'lucide-react';
import { useAppDispatch, useAppSelector } from "../../../lib/hooks";
import { useRouter } from "next/navigation";
import { CourseCard } from "../../../components/courses/CourseCard";
import { fetchCoursesList } from "../../../lib/features/courses/coursesSlice";

export default function CoursesPage() {
    const dispatch = useAppDispatch()
    useEffect(() => {
        dispatch(fetchCoursesList())
    }, [dispatch])
    const navigate = useRouter();
    const { userProfile } = useAppSelector(state => state.userState);
    const { courses } = useAppSelector(state => state.courseListState);
    const { enrollments } = useAppSelector(state => state.enrollmentsState)
    const [searchTerm, setSearchTerm] = useState('');
    const [filter, setFilter] = useState<'all' | 'available' | 'enrolled'>('all');

    const enrolledCourseIds = enrollments.map(v => v.course.id)

    // Make sure we're not accidentally filtering out courses
    const filteredCourses = courses.filter(course => {
        const matchesSearch = 
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase());
        
        if (!matchesSearch) return false;

        switch (filter) {
        case 'enrolled':
            return enrolledCourseIds.includes(course.id);
        case 'available':
            return !enrolledCourseIds.includes(course.id) && (course.enrollments?.length || 0) < course.maxStudents;
        default:
            return true;
        }
    });

    const getEnrollmentStatus = (courseId: string) => {
        if (enrolledCourseIds.includes(courseId)) {
            const enrollment = enrollments.find(c => c.course.id === courseId);
            return {
                status: 'enrolled' as const,
                progress: enrollment?.progress || 0
            };
        }
        const course = courses.find(c => c.id === courseId);
        return {
            status: (course?.enrollments?.length || 0) >= (course?.maxStudents || 0) ? 'full' as const : 'available' as const,
            progress: undefined
        };
    };

    const stats = {
        totalCourses: courses.length,
        enrolledCourses: enrolledCourseIds.length,
        availableCourses: courses.filter(c => !enrolledCourseIds.includes(c.id) && (c.enrollments?.length || 0) < c.maxStudents).length
    };

    return <AuthContextProvider>
        <div className="p-6 dark:text-slate-100">
        <div className="mb-6">
            <h1 className="text-2xl font-bold">Available Courses</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
            Explore our comprehensive selection of AI and Machine Learning courses
            </p>
        </div>

        {/* Course Statistics */}
        <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <div className="flex items-center gap-4 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
            <div className="rounded-lg bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
                <BookOpen className="h-5 w-5" />
            </div>
            <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Courses</p>
                <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                {stats.totalCourses}
                </p>
            </div>
            </div>
            <div className="flex items-center gap-4 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
            <div className="rounded-lg bg-green-100 p-3 text-green-600 dark:bg-green-900/50 dark:text-green-400">
                <Users className="h-5 w-5" />
            </div>
            <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">My Enrolled Courses</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {stats.enrolledCourses}
                </p>
            </div>
            </div>
            <div className="flex items-center gap-4 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
            <div className="rounded-lg bg-purple-100 p-3 text-purple-600 dark:bg-purple-900/50 dark:text-purple-400">
                <BookOpen className="h-5 w-5" />
            </div>
            <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Available to Enroll</p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {stats.availableCourses}
                </p>
            </div>
            </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
                type="text"
                placeholder="Search courses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full rounded-lg border bg-white pl-10 pr-4 py-2 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
            />
            </div>
            <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as 'all' | 'available' | 'enrolled')}
                className="rounded-lg border bg-white px-3 py-2 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
            >
                <option value="all">All Courses</option>
                <option value="available">Available to Enroll</option>
                <option value="enrolled">My Enrolled Courses</option>
            </select>
            </div>
        </div>

        {/* Course Grid */}
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {filteredCourses.map((course) => {
            const { status, progress } = getEnrollmentStatus(course.id as string);
            return (
                <CourseCard
                    key={course.id}
                    course={course}
                    progress={progress as number}
                    enrollmentStatus={status}
                    onClick={() => navigate.push(`/courses/${course.id}`)}
                />
            );
            })}
        </div>

        {filteredCourses.length === 0 && (
            <div className="flex h-40 items-center justify-center rounded-lg border border-dashed text-gray-500 dark:border-gray-700">
            No courses found matching your criteria
            </div>
        )}
        </div>
    </AuthContextProvider>
}