"use client";
import { useEffect, useState } from 'react';
import { Card, CardContent, Typography, Grid, Container } from '@mui/material';
import { PrimaryButton } from '../ui/buttons';
import { AddCourseDialog } from '../dialog/AddCourse';
import { useAppDispatch, useAppSelector } from '../../lib/hooks';
import { addCourseInfo, fetchCoursesList } from '../../lib/features/courses/coursesSlice';
import { RichTextEditor } from '../ui/RichTextEditor';
import { useRouter } from 'next/navigation';
import { SecondaryButton } from '../ui/buttons/secondary-button';

export function CourseListAdmin() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { courses } = useAppSelector((state) => state.courseListState);
  const [addCourseDialog, setAddCourseDialog] = useState(false);  

  useEffect(() => {
    dispatch(fetchCoursesList());  
  }, []);

  const handleCreateCourse = () => {
    // Logic to create a course (this will be a modal, form, or redirect)
    setAddCourseDialog(true);
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <Container maxWidth="lg" className="flex flex-col items-center py-12 gap-2">
        <Typography variant="h4" className="text-4xl font-bold text-gray-900 mb-6">
          Your Courses
        </Typography>
        
        {courses.length === 0 ? (
          <div className="text-center">
            <Typography variant="h6" className="text-xl text-gray-600 mb-4">
              You have zero courses.
            </Typography>
            <SecondaryButton 
              variant="contained" 
              color="primary" 
              onClick={handleCreateCourse}
            >
              Create a New Course
            </SecondaryButton>
          </div>
        ) : (
          <div className='flex flex-col justify-between w-full gap-2'>
            <div className='flex justify-end'>
              <SecondaryButton 
                  variant="contained" 
                  color="primary" 
                  onClick={handleCreateCourse}
                >
                  Create a New Course
              </SecondaryButton>
            </div>
            <Grid container spacing={4}>
              {courses.map((course) => (
                <Grid item xs={12} sm={6} md={4} key={course.id}>
                  <Card>
                    <CardContent className="flex flex-col gap-2">
                      <Typography variant="h5" className="font-semibold text-gray-900">
                        {course.title}
                      </Typography>
                      <RichTextEditor readonly inputHtml={course.description} />
                      <PrimaryButton 
                        variant="outlined" 
                        color="primary"
                        className="w-full mt-2"
                        onClick={() => router.push(`/courses/${course.id}`)}
                      >
                        View Course
                      </PrimaryButton>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </div>
        )}
      </Container>
      {addCourseDialog && <AddCourseDialog 
          handleDialogCancel={() => setAddCourseDialog(false)} 
          handleDialogOk={(formData) => {
            dispatch(addCourseInfo({ formData }));
            setAddCourseDialog(false);
          }} 
      />}
    </div>
  );
}
