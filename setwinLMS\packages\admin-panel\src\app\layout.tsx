import {Inter} from "next/font/google";
import "../styles/globals.css";
import "../styles/editor.css";
import "../styles/command-pallette.css";
import "../styles/nprogress.css";
import "../styles/react-datepicker.css";
import { ThemeProvider } from "next-themes";
import { Header } from "../components/Header";
import { Footer } from "../components/Footer";
import { Growl } from "../components/notifications/Growl";
import { SITE_TITLE, SITE_NAME, SITE_AUTHOR, SITE_DESCRIPTION, SITE_KEYWORDS, SITE_URL } from "../constants/seo-variables";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { ReactNode } from "react";
import StoreProvider from "../context/StoreProvider";
import { CssBaseline } from "@mui/material";

const inter = Inter({subsets: ["latin"]});
export const metadata = {
  title: SITE_NAME,
  description: SITE_DESCRIPTION,
};
interface IRootLayout {
  children: ReactNode;
}
const RootLayout:React.FC<IRootLayout> = ({children}) => {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta property="og:site_name" content={SITE_NAME} />
        <meta property="og:title" content={SITE_TITLE} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={SITE_URL} />
        <meta property="og:image" content={`${SITE_URL}/favicons/apple-touch-icon.png`}></meta>
        <meta property="og:description" content={SITE_DESCRIPTION} />
        <meta name="author" content={SITE_AUTHOR} />
        <meta name="keywords" content={SITE_KEYWORDS} />
        <meta name="description" content={SITE_DESCRIPTION} />
        <link rel="shortcut icon" href="/favicons/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicons/favicon-16x16.png" />
        <link rel="manifest" href="/favicons/site.webmanifest" />
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1302626070893007" crossOrigin="anonymous"></script>
        {/* Any additional external scripts should be added here */}
      </head>
      <body className={`${inter.className}`}>
        <StoreProvider>
        <ThemeProvider defaultTheme="light">
          <CssBaseline />
          <Growl />
          <main className="flex flex-col gap-8 h-full w-full">
            {children}
          </main>
        </ThemeProvider>
        </StoreProvider>
        <SpeedInsights />
        <Analytics />
      </body>
    </html>
  );
}

export default RootLayout;