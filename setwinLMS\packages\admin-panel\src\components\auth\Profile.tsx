"use client";
import { <PERSON><PERSON>, FormControlLabel, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { PrimaryButton } from "../ui/buttons";
import { validateProfileForm } from "../../lib/utils/app_utils";
import { ChangeEvent, useEffect, useState } from "react";
import { IProfileForm } from "../../types/app";
import { gaCustomEvent } from "../../lib/utils/gtag_utils";
import { useAppDispatch, useAppSelector } from "../../lib/hooks";
import { upsertCurrentUser } from "../../lib/features/currentUser/currentUserSlice";
import { useRouter } from 'next/navigation'
import { unwrapResult } from "@reduxjs/toolkit";
import { INPUT_STYLE } from "../../constants/component-variables";

export const Profile = () => {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { userProfile } = useAppSelector(state => state.userState);
    const [formData, setFormData] = useState<IProfileForm>(userProfile);
    const [errorMessages, setErrorMessages] = useState<string[]>([]);
    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    useEffect(() => {
        // Only update formData if userProfile changes 
        // and only if it's different from the current formData
        let newUserProfile = formData;
        if (userProfile && JSON.stringify(userProfile) !== JSON.stringify(formData)) {
            newUserProfile = userProfile;
        }
        setFormData({ ...newUserProfile });
    }, [userProfile, formData]); 
    const upsertProfile = async () => {
        const messages = validateProfileForm(formData);
        if (messages.length) {
          setErrorMessages(messages);
          return;
        }
        gaCustomEvent({
            "action": "btn_click",
            "category": "click",
            "label": "update_profile",
            "value": formData
        });
        dispatch(upsertCurrentUser({ formData }))
        .then(unwrapResult)
        .then(res => {
            if(!res.isError && !userProfile.user_id) {
                router.push('/courses');
            }
        })
    }

    return (<>
        {errorMessages && (
                errorMessages.map((message, idx) => <div className="text-red-500 py-1 w-[40%] text-left" key={idx}>
                    <p> {message} </p>
                </div>)
        )}
        <form className="flex flex-col gap-4 w-full sm:w-[40%] pt-8">
        <Typography variant="h4" >Hello!</Typography>
        <Typography variant="h6" className=" pb-3">Here are your profile details</Typography>
            <TextField
                label='First Name'
                name="first_name"
                value={formData?.first_name}
                onChange={handleChange}
                sx={INPUT_STYLE}
                fullWidth
                required
                variant="filled"
            />
            <TextField
                label='Last Name'
                name="last_name"
                value={formData?.last_name}
                onChange={handleChange}
                sx={INPUT_STYLE}
                fullWidth
                required
                variant="filled"
            />
            
            {false && userProfile.user_id && <TextField
                label="Email Id"
                name="emailId"
                type="text"
                value={userProfile?.email}
                sx={INPUT_STYLE}
                fullWidth
                variant="filled"
                disabled
            />}
            <div className="flex justify-center">
              <PrimaryButton className="w-[47%] ml-auto mr-auto" type="button" onClick={upsertProfile}>Save Changes</PrimaryButton>
            </div>
        </form>
    </>)
}