import { TextField, Typography } from "@mui/material";
import { INPUT_STYLE } from "../../constants/component-variables";
import { RichTextEditor } from "../ui/RichTextEditor";
import { IModule } from "../../types/app";
import { useState } from "react";
import { SecondaryButton } from "../ui/buttons/secondary-button";
import { PrimaryButton } from "../ui/buttons";

interface EditModuleProps {
    activeModule: IModule;
    onSave: (updatedModule: IModule) => void;
    onClose: () => void;
}
export const EditModule:React.FC<EditModuleProps> = ({activeModule, onSave, onClose}) => {
    const [formData, setFormData] = useState<{"title": string, "description": string }>({
        "title": activeModule?.title || '',
        "description": activeModule?.description || ''
    });
    const [errors, setErrors] = useState({ title: false, description: false });
    const validateForm = () => {
        const updatedErrors: { title: boolean, description: boolean } = { title: false, description: false };
        updatedErrors.title = formData.title.trim() === '';
        updatedErrors.description = formData.description.trim() === '';
        setErrors(updatedErrors);
    
        return formData.title.length > 0 && formData.description.length > 0;
    }
    
    const handleRTEChange = (html: string) => {
        updateFormData("description", html);
    };
    
    const updateFormData = (key: string, value: string) => {
        setFormData({ ...formData, [key]: value });
    }
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        updateFormData(e.target.name, e.target.value);
    };
    
    const handleSave = () => {
        if (!validateForm()) {
            return;
        }
        const newFormData: IModule = {
            ...activeModule,
            ...formData
        }
        onSave(newFormData);
    }
    return (
        <div className="p-4 flex flex-col h-full gap-4">
            <form className="pt-4 flex flex-col gap-2">
                <TextField
                    label='Title'
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    fullWidth
                    required
                    sx={INPUT_STYLE}
                    variant="filled"
                    error={errors.title}
                    helperText={errors.title ? 'Title is a required field' : ''}
                    inputProps={{ maxLength: 80 }}
                />
                <div className='flex flex-col gap-2'>
                    <Typography variant="body2" className={errors.description ? "text-red-600" : "text-gray-600"}>Description :</Typography>
                    <div className= {errors.description ? "border border-solid border-red-600" : "border border-solid border-gray-600"}>
                        <RichTextEditor onChange={handleRTEChange} inputHtml={formData.description} />
                    </div>
                    <Typography variant='body2' className='text-sm text-red-600'>{errors.description ? 'Description is a required field' : ''}</Typography>
                </div>
            </form>
            <div className="flex justify-end gap-4 mt-auto">
                <SecondaryButton onClick={handleSave}> Save </SecondaryButton>
                <PrimaryButton onClick={onClose}> Cancel </PrimaryButton>
            </div>
        </div>
    );
}