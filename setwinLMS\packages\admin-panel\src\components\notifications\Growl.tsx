"use client"
import { <PERSON><PERSON>, Snackbar } from "@mui/material"
import { useAppDispatch, useAppSelector } from "../../lib/hooks";
import { removeNotification } from "../../lib/features/notifications/notificationSlice";

export const Growl = () => {
    const dispatch = useAppDispatch();
    const { notifications } = useAppSelector(state => state.notificationState);
    const handleAlertClose = (id: string) => {
        dispatch(removeNotification(id));
    }
    return notifications.map((v) => {
        return <Snackbar key={v.id} open={true} autoHideDuration={6000} 
            onClose={() => handleAlertClose(v.id as string)} anchorOrigin={{ vertical: 'top', horizontal: 'right' }}>
            <Alert severity={v.severity} onClose={() => handleAlertClose(v.id as string)}>
                {v.message}
            </Alert>
        </Snackbar>
    });
}