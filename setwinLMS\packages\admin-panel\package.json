{"name": "@setwin/admin-panel", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "start:port": "next start --port=9000", "start:docker": "docker compose up -d"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^5.12.3", "@mui/material": "^5.12.3", "@mui/system": "^5.12.3", "@mui/x-date-pickers": "^7.22.2", "@next/third-parties": "^15.0.3", "@reduxjs/toolkit": "^2.3.0", "@tailwindcss/typography": "^0.5.9", "@tiptap/core": "2.0.4", "@tiptap/extension-blockquote": "2.0.4", "@tiptap/extension-bold": "2.0.4", "@tiptap/extension-bubble-menu": "2.0.4", "@tiptap/extension-bullet-list": "2.0.4", "@tiptap/extension-code": "2.0.4", "@tiptap/extension-code-block": "2.0.4", "@tiptap/extension-color": "2.0.4", "@tiptap/extension-document": "2.0.4", "@tiptap/extension-dropcursor": "2.0.4", "@tiptap/extension-floating-menu": "2.0.4", "@tiptap/extension-font-family": "2.0.4", "@tiptap/extension-gapcursor": "2.0.4", "@tiptap/extension-hard-break": "2.0.4", "@tiptap/extension-heading": "2.0.4", "@tiptap/extension-highlight": "2.0.4", "@tiptap/extension-history": "2.0.4", "@tiptap/extension-horizontal-rule": "2.0.4", "@tiptap/extension-image": "2.0.4", "@tiptap/extension-italic": "2.0.4", "@tiptap/extension-link": "2.0.4", "@tiptap/extension-list-item": "2.0.4", "@tiptap/extension-mention": "2.0.4", "@tiptap/extension-ordered-list": "2.0.4", "@tiptap/extension-paragraph": "2.0.4", "@tiptap/extension-placeholder": "2.0.4", "@tiptap/extension-strike": "2.0.4", "@tiptap/extension-subscript": "2.0.4", "@tiptap/extension-superscript": "2.0.4", "@tiptap/extension-table": "2.0.4", "@tiptap/extension-table-cell": "2.0.4", "@tiptap/extension-table-header": "2.0.4", "@tiptap/extension-table-row": "2.0.4", "@tiptap/extension-task-item": "2.0.4", "@tiptap/extension-task-list": "2.0.4", "@tiptap/extension-text": "2.0.4", "@tiptap/extension-text-align": "2.0.4", "@tiptap/extension-text-style": "2.0.4", "@tiptap/extension-underline": "2.0.4", "@tiptap/pm": "2.0.4", "@tiptap/react": "2.0.4", "@tiptap/suggestion": "2.0.4", "@vercel/analytics": "^1.3.1", "@vercel/speed-insights": "^1.0.12", "dayjs": "^1.11.13", "jwt-decode": "^4.0.0", "lucide-react": "^0.344.0", "mui-tiptap": "^1.12.0", "next": "14.2.5", "next-themes": "^0.3.0", "nprogress": "^0.2.0", "react": "18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "18.3.1", "react-markdown": "9.0.1", "react-redux": "^9.1.2", "recharts": "^2.12.2", "remark-gfm": "^4.0.0", "sharp": "^0.32.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "touch-cli": "^0.0.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/gtag.js": "0.0.20", "@types/jest": "^29.5.12", "@types/node": "20.8.3", "@types/nprogress": "^0.2.0", "@types/react": "18.3.1", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.3.0", "@types/uuid": "^8.3.4", "autoprefixer": "^10.4.7", "eslint": "8.51.0", "eslint-config-next": "^14.2.11", "tailwindcss": "^3.1.6", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2"}}