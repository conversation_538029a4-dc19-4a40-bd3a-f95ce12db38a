"use client"
import React, { useState } from 'react';
import { Bell, Inbox, LayoutDashboard, Settings as SettingsIcon, Star, User, DollarSign, Check } from 'lucide-react';
import { useSettings } from '../../../context/SettingsContext';
import { useCurrency, currencies } from '../../../context/CurrencyContext';
import { useRouter } from 'next/navigation';

const settingsSections = [
  {
    title: 'Preferences',
    icon: Star,
    items: [
      { label: 'Wishlist', href: '#', description: 'Manage your course wishlist' },
      { label: 'Favorites', href: '#', description: 'View and organize your favorite courses' },
      { label: 'Learning Reminders', href: '#', description: 'Set up course completion reminders' }
    ]
  },
  {
    title: 'Notifications',
    icon: Bell,
    items: [
      { label: 'Email Notifications', href: '#', key: 'emailnotifications', description: 'Manage email alert preferences' },
      { label: 'Push Notifications', href: '#', key: 'pushnotifications', description: 'Control browser notifications' },
      { label: 'Course Updates', href: '#', key: 'courseupdates', description: 'Get notified about course changes' }
    ]
  },
  {
    title: 'Messages',
    icon: Inbox,
    items: [
      { label: 'Inbox', href: '#', description: 'View your messages' },
      { label: 'Announcements', href: '#', description: 'Important platform updates' },
      { label: 'Discussion Forums', href: '#', description: 'Course discussions and community' }
    ]
  },
  {
    title: 'Account',
    icon: User,
    items: [
      { label: 'Profile Settings', href: '#', description: 'Update your personal information' },
      { label: 'Password & Security', href: '#', description: 'Manage your account security' },
      { label: 'Linked Accounts', href: '#', description: 'Connect external accounts' }
    ]
  },
  {
    title: 'Dashboard',
    icon: LayoutDashboard,
    items: [
      { label: 'View Dashboard', href: '/dashboard', description: 'Go to your learning dashboard' },
      { label: 'Customize View', href: '#', description: 'Personalize dashboard layout' },
      { label: 'Analytics', href: '#', description: 'View detailed learning analytics' }
    ]
  }
];

export default function SettingsPage() {
  const navigate = useRouter();
  const { currency, setCurrency } = useCurrency();
  const { settings, updateSettings, saveSettings, hasUnsavedChanges } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await saveSettings();
      setShowSaveSuccess(true);
      setTimeout(() => setShowSaveSuccess(false), 3000);
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleItemClick = (href: string) => {
    if (!href.startsWith('#')) {
      navigate.push(href);
    }
  };

  return (
    <div className="p-6 dark:text-slate-100">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Settings</h1>
        <button
          onClick={handleSave}
          disabled={!hasUnsavedChanges || isSaving}
          className={`flex items-center gap-2 rounded-lg px-4 py-2 text-white transition-colors ${
            hasUnsavedChanges
              ? 'bg-indigo-600 hover:bg-indigo-700'
              : 'bg-gray-400 cursor-not-allowed'
          }`}
        >
          {isSaving ? (
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
          ) : showSaveSuccess ? (
            <Check className="h-4 w-4" />
          ) : (
            <SettingsIcon className="h-4 w-4" />
          )}
          <span>{isSaving ? 'Saving...' : showSaveSuccess ? 'Saved!' : 'Save Changes'}</span>
        </button>
      </div>

      {/* Currency Settings */}
      <div className="mb-6 rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-3">
          <div className="rounded-lg bg-indigo-100 p-2 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
            <DollarSign className="h-5 w-5" />
          </div>
          <h2 className="text-lg font-semibold">Currency Settings</h2>
        </div>
        <div className="flex items-center gap-4">
          <label className="text-sm font-medium">Display Currency:</label>
          <select
            value={currency.code}
            onChange={(e) => {
              const selected = currencies.find(c => c.code === e.target.value);
              if (selected) setCurrency(selected);
            }}
            className="rounded-lg border border-gray-300 px-3 py-2 dark:border-gray-600 dark:bg-gray-700"
          >
            {currencies.map(curr => (
              <option key={curr.code} value={curr.code}>
                {curr.name} ({curr.symbol})
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {settingsSections.map((section) => {
          const Icon = section.icon;
          return (
            <div
              key={section.title}
              className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800"
            >
              <div className="mb-4 flex items-center gap-3">
                <div className="rounded-lg bg-indigo-100 p-2 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
                  <Icon className="h-5 w-5" />
                </div>
                <h2 className="text-lg font-semibold">{section.title}</h2>
              </div>
              <div className="space-y-2">
                {section.items.map((item) => (
                  <div key={item.label} className="flex items-center justify-between">
                    <button
                      onClick={() => handleItemClick(item.href)}
                      className="flex flex-1 items-center justify-between rounded-lg px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <div>
                        <div>{item.label}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {item.description}
                        </div>
                      </div>
                    </button>
                    {section.title === 'Notifications' && (item as any).key && (
                      <label className="relative ml-2 inline-flex cursor-pointer items-center">
                        <input
                          type="checkbox"
                          checked={settings.notifications[(item as any).key as keyof typeof settings.notifications]}
                          onChange={(e) => {
                            updateSettings({
                              notifications: {
                                ...settings.notifications,
                                [(item as any).key as string]: e.target.checked
                              }
                            });
                          }}
                          className="peer sr-only"
                        />
                        <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-indigo-600 peer-checked:after:translate-x-full peer-checked:after:border-white dark:bg-gray-700"></div>
                      </label>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}