import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { INotification } from '../../../types/app';

interface NotificationState {
  notifications: INotification[];
}

const initialState: NotificationState = {
  notifications: []
};

export const notificationSlice = createSlice({
  name: 'notificationState',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<INotification>) => {
      state.notifications.push({
        ...action.payload,
        id: action.payload.id || Date.now().toString(),
        duration: action.payload.duration || 6000
      });
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification: INotification) => notification.id !== action.payload
      );
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
    }
  }
});

export const { 
  addNotification, 
  removeNotification, 
  clearAllNotifications 
} = notificationSlice.actions;

export default notificationSlice.reducer;