import React from 'react';
import { IProfileForm } from '../../types/app';

interface StudentListProps {
  students: IProfileForm[];
  onSelectStudent: (student: IProfileForm) => void;
}

export function StudentList({ students, onSelectStudent }: StudentListProps) {
  return (
    <div className="divide-y divide-gray-200 rounded-lg bg-white shadow dark:divide-gray-700 dark:bg-gray-800">
      {students.map((student) => (
        <div
          key={student.user_id}
          className="flex cursor-pointer items-center gap-4 p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
          onClick={() => onSelectStudent(student)}
        >
          <img
            src={student.avatar_url}
            alt={student.first_name + ' ' + student.last_name}
            className="h-10 w-10 rounded-full"
          />
          <div className="flex-1">
            <h3 className="font-medium text-gray-900 dark:text-white">
              {student.first_name + ' ' + student.last_name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {student.email}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {(student as any).enrolledCourses.length} Courses
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(
                (student as any).enrolledCourses.reduce((acc: any, course: { progress: any; }) => acc + course.progress, 0) /
                (student as any).enrolledCourses.length
              )}% Avg. Progress
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}