import React from 'react';
import type { EditorOptions } from "@tiptap/core";
import { useCallback, useRef } from "react";
import {
  LinkBubbleMenu,
  RichTextEditor as MUIRichTextEditor,
  RichTextReadOnly,
  TableBubbleMenu,
  insertImages,
  type RichTextEditorRef,
} from "mui-tiptap";

import { Box } from '@mui/material';
import { EditorMenuControls } from './EditorMenuControls';
import { useExtensions } from './useExtensions';
import { convertToBase64 } from '../../lib/utils/app_utils';

interface RichTextEditorProps {
    onChange?: (html: string) => void;
    readonly?: boolean;
    inputHtml?: string;
}

function fileListToImageFiles(fileList: FileList): File[] {
    // You may want to use a package like attr-accept
    // (https://www.npmjs.com/package/attr-accept) to restrict to certain file
    // types.
    return Array.from(fileList).filter((file) => {
      const mimeType = (file.type || "").toLowerCase();
      return mimeType.startsWith("image/");
    });
}
export const RichTextEditor: React.FC<RichTextEditorProps> = ({ onChange, readonly, inputHtml }) => {
    const rteRef = useRef<RichTextEditorRef>(null);
    const extensions = useExtensions({
        placeholder: "Add your own content here...",
    });

    const handleNewImageFiles = useCallback(
        async (files: File[], insertPosition?: number): Promise<void> => {
            if (!rteRef.current?.editor) {
                return;
            }
    
            const base64Promises = await Promise.all(files.map((file) => {
              return convertToBase64(file);
            }));
            const attributesForImageFiles = files.map((file, index) => {
                return {src: base64Promises[index], alt: file.name}
            }) as {src: string, alt: string}[]
    
            insertImages({
                images: attributesForImageFiles,
                editor: rteRef.current.editor,
                position: insertPosition,
            });
        }, []);
    
    // Allow for dropping images into the editor
    const handleDrop: NonNullable<EditorOptions["editorProps"]["handleDrop"]> =
        useCallback((view, event, _slice, _moved) => {
        if (!(event instanceof DragEvent) || !event.dataTransfer) {
            return false;
        }

        const imageFiles = fileListToImageFiles(event.dataTransfer.files);
        if (imageFiles.length > 0) {
            const insertPosition = view.posAtCoords({
            left: event.clientX,
            top: event.clientY,
            })?.pos;

            handleNewImageFiles(imageFiles, insertPosition);

            // Return true to treat the event as handled. We call preventDefault
            // ourselves for good measure.
            event.preventDefault();
            return true;
        }

        return false;
        }, [handleNewImageFiles]);

    // Allow for pasting images
    const handlePaste: NonNullable<EditorOptions["editorProps"]["handlePaste"]> =
        useCallback(
        (_view, event, _slice) => {
        if (!event.clipboardData) {
            return false;
        }

        const pastedImageFiles = fileListToImageFiles(
            event.clipboardData.files,
        );
        if (pastedImageFiles.length > 0) {
            handleNewImageFiles(pastedImageFiles);
            // Return true to mark the paste event as handled. This can for
            // instance prevent redundant copies of the same image showing up,
            // like if you right-click and copy an image from within the editor
            // (in which case it will be added to the clipboard both as a file and
            // as HTML, which Tiptap would otherwise separately parse.)
            return true;
        }

        // We return false here to allow the standard paste-handler to run.
        return false;
        }, [handleNewImageFiles]);

    return (<Box sx={{
        // An example of how editor styles can be overridden. In this case,
        // setting where the scroll anchors to when jumping to headings. The
        // scroll margin isn't built in since it will likely vary depending on
        // where the editor itself is rendered (e.g. if there's a sticky nav
        // bar on your site).
        "& .ProseMirror": {
          "& h1, & h2, & h3, & h4, & h5, & h6": {
            scrollMarginTop: !readonly ? 50 : 0,
          }
        },
      }}>
        {readonly ? <RichTextReadOnly
              content={inputHtml}
              extensions={extensions}
        /> : 
        <MUIRichTextEditor
            ref={rteRef}
            content={inputHtml}
            extensions={extensions}
            editable={true}
            editorProps={{
                handleDrop: handleDrop,
                handlePaste: handlePaste,
            }}
            renderControls={() => <EditorMenuControls />}
            onBlur={(props) => {
                onChange?.(props.editor.getHTML())
            }}
            RichTextFieldProps={{
            // The "outlined" variant is the default (shown here only as
            // example), but can be changed to "standard" to remove the outlined
            // field border from the editor
                variant: "outlined",
                MenuBarProps: {
                    hide: false,
                },
                className: 'bg-white'
            }}
        >
        {() => (
          <>
            <LinkBubbleMenu />
            <TableBubbleMenu />
          </>
        )}
        </MUIRichTextEditor>}
    </Box>
      );
}