{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "strict": true, "noEmit": true, "emitDeclarationOnly": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}], "skipLibCheck": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../apps/admin-panel/.next/types/**/*.ts", "../../dist/apps/admin-panel/.next/types/**/*.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]}