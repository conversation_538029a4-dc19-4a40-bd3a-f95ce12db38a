import { AlertColor } from '@mui/material';
import { addNotification } from '../../lib/features/notifications/notificationSlice';
import { useAppDispatch } from '../../lib/hooks';

const GrowlWrapper = () => {
    const dispatch = useAppDispatch();

    const notify = (message: string, severity: AlertColor = 'info') => {
        dispatch(addNotification({ message, severity }));
    };

    return { notify };
};

export default GrowlWrapper;
