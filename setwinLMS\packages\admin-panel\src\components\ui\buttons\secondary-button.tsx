import { Button, ButtonProps } from "@mui/material";

export const SecondaryButton: React.FC<ButtonProps> = ({
  children,
  className = "",
  onClick,
  type = "button",
  disabled = false,
  size = "md",
  variant = 'outlined',
}) => (
  <Button
    type={type}
    className={`${className} border border-custom-primary font-medium duration-300 ${
      size === "sm"
        ? "rounded px-3 py-2 text-xs"
        : size === "md"
        ? "rounded-md px-3.5 py-2 text-sm"
        : "rounded-lg px-4 py-2 text-base"
    } ${disabled ? "cursor-not-allowed opacity-70 hover:opacity-70" : ""}`}
    sx={{
      backgroundColor: "rgb(26, 75, 91)",
      borderColor: "white",
      color: "white",
      "&:hover": {
        backgroundColor: "rgb(26, 75, 91)",
        borderColor: "white",
      }
    }}
    onClick={onClick}
    disabled={disabled}
    variant={variant}
  >
    {children}
  </Button>
);
