"use client"
import { <PERSON><PERSON>ield, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { PrimaryButton } from "./ui/buttons";
import { gaCustomEvent } from "../lib/utils/gtag_utils";
import { validateSignupForm } from "../lib/utils/app_utils";
import { ChangeEvent, useState } from "react";
import { ISignupForm, ITranportResponse } from "../types/app";
import { transport } from "../lib/network/transport";
import GrowlWrapper from "./notifications/GrowlWrapper";

const INPUT_STYLE = {
  backgroundColor: 'white'
};
export const Signup = () => {
    const { notify } = GrowlWrapper();
    const router = useRouter();
    const [formData, setFormData] = useState<ISignupForm>({
        "first_name": "",
        "last_name": "",
        "email": "",
        "phoneNumber": "",
        "password": "",
        "confirmPassword": "",
        role: 'student'
    });
    const [errorMessages, setErrorMessages] = useState<string[]>([]);
    
    const signUp = async () => {
        const messages = validateSignupForm(formData);
        if (messages.length) {
          setErrorMessages(messages);
          return;
        }
        gaCustomEvent({
          "action": "btn_click",
          "category": "click",
          "label": "sign_up_btn",
          "value": formData.email
        })
        try {
          (await transport.post('signup', formData)) as ITranportResponse;
          setFormData({
            "first_name": "",
            "last_name": "",
            "email": "",
            "phoneNumber": "",
            "password": "",
            "confirmPassword": "",
            role: 'student'
          });
          router.push('/login');
        } catch(error) {
          const msg = (error as ITranportResponse).error.message;
          notify(msg, 'error');
        }
    };
    
    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData({ ...formData, [e.target.name]: e.target.value });
    };
    return <div className="flex flex-col w-full items-center py-8">
        {errorMessages && (
              errorMessages.map((message, idx) => <div className="text-red-500 py-1 md:w-[40%] w-full text-left" key={idx}>
                <p> {message} </p>
              </div>)
          )}
        <form className="flex flex-col gap-4 md:w-[40%] w-full">
            <Typography variant="h4" className="text-center">Create Account</Typography>
            {/* <Typography variant="h6" className="text-center">Sign Up to get started on a great platform</Typography> */}
            <TextField
              label='First Name'
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              sx={INPUT_STYLE}
              fullWidth
              required
              variant="filled"
            />
            <TextField
              label='Last Name'
              name="last_name"
              value={formData.last_name}
              onChange={handleChange}
              sx={INPUT_STYLE}
              fullWidth
              required
              variant="filled"
            />
            <TextField
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              sx={INPUT_STYLE}
              fullWidth
              required
              variant="filled"
            />
            <TextField
              label="Contact Number"
              name="phoneNumber"
              type="number"
              value={formData.phoneNumber}
              onChange={handleChange}
              sx={INPUT_STYLE}
              fullWidth
              required
              variant="filled"
            />
            <TextField
              label="Password"
              name="password"
              value={formData.password}
              type="password"
              onChange={handleChange}
              sx={INPUT_STYLE}
              fullWidth
              variant="filled"
            />
            <TextField
              label="Confirm Password"
              name="confirmPassword"
              value={formData.confirmPassword}
              type="password"
              onChange={handleChange}
              sx={INPUT_STYLE}
              fullWidth
              variant="filled"
            />

            <div className="flex justify-center">
              <PrimaryButton className="w-[47%] ml-auto mr-auto" type="button" onClick={signUp}>Sign Up</PrimaryButton>
            </div>

            <div className="mt-4 text-center">
                {"Already have an account? "}
                <Link href="/login" className="underline"> Login</Link>
            </div>
        </form>
    </div>
}