import React, { useState, useEffect } from 'react';
import { ILesson, IModule } from '../../types/app';
import { DragDropList } from './DragDropList';
import { DropResult } from 'react-beautiful-dnd';
import { ConfirmDialog } from '../dialog/DeleteDialog';
import { EditDrawer } from '../ui/EditDrawer';
import { EditLesson } from './EditLesson';
import { SecondaryButton } from '../ui/buttons/secondary-button';
import { useAppDispatch, useAppSelector } from '../../lib/hooks';
import { fetchLessonsByModule } from '../../lib/features/courses/lessonsSlice';
import { addLessonInfoToModule, deleteLessonInfoToModule, updateLessonInfoToModule } from '../../lib/features/courses/lessonSlice';

interface ILessonListProps {
    moduleId: string;
}

export const LessonList:React.FC<ILessonListProps> = ({ moduleId }) => {
    const dispatch = useAppDispatch();
    const { lessons } = useAppSelector((state) => state.lessonsState);
    const [expandLesson, setExpandLesson] = useState<boolean>(false); // Track edit lesson drawer
    const [activeLesson, setActiveLesson] = useState<ILesson | null>(null);
    const [deleteLessonDialogOpen, setDeleteLessonDialogOpen] = useState(false); // Delete lesson confirmation dialog
    // Simulate fetching lessons based on the moduleId
    useEffect(() => {
        const fetchLessons = () => {
            dispatch(fetchLessonsByModule({ moduleId }));
        };
        fetchLessons();
    }, []);

    const handleLessonDragEnd = (result: DropResult) => {
        const { destination, source } = result;

        if (!destination || destination.index === source.index) return;

        const reorderedLessons = Array.from(lessons);
        const [removed] = reorderedLessons.splice(source.index, 1);
        reorderedLessons.splice(destination.index, 0, removed);

        console.log(reorderedLessons);
    };

    const handleNewLesson = () => {
        setExpandLesson(true);
        setActiveLesson(null);
    };

    const handleEditLesson = (lesson: IModule | ILesson) => {
        setExpandLesson(true);
        setActiveLesson(lesson as ILesson);
    };
    
    const handleEditLessonClose = () => {
        setExpandLesson(false);
        setActiveLesson(null);
    }
    
    const handleEditLessonSave = (updatedInfo: ILesson) => {
        console.log(`Saving lesson with ID: ${updatedInfo?.id}`);
        if (updatedInfo && updatedInfo.id) {
            dispatch(updateLessonInfoToModule({ moduleId, formData: updatedInfo }));
        } else {
            updatedInfo.position = lessons.length;
            dispatch(addLessonInfoToModule({ moduleId, formData: updatedInfo }));
        }
        setExpandLesson(false);
        setActiveLesson(null);
    }

    const handleDeleteLesson = (lesson: IModule | ILesson) => {
        console.log('Delete Lesson:', lesson);
        setActiveLesson(lesson as ILesson);
        setDeleteLessonDialogOpen(true);
    };
    
    const handleDeleteLessonConfirm = () => {
        console.log('Deleting lesson', activeLesson);
        dispatch(deleteLessonInfoToModule({ lessonId: (activeLesson as ILesson).id as string, moduleId }));
        setDeleteLessonDialogOpen(false);
    }

    const handleDeleteLessonClose = () => {
        setActiveLesson(null);
        setDeleteLessonDialogOpen(false);
    }

    return (<div className='flex flex-col'>
        <div className="flex flex-row justify-end items-center p-2">
            <button
            onClick={handleNewLesson}
            className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            Add New Lesson
          </button>
        </div>
        <DragDropList
            items={lessons}
            onDragEnd={handleLessonDragEnd}
            onEdit={handleEditLesson}
            onDelete={handleDeleteLesson}
            isLessonList={true}
            droppableId="lesson-list"
        />
        {/* Delete Lesson Confirmation Dialog */}
        <ConfirmDialog
            open={deleteLessonDialogOpen} 
            onClose={handleDeleteLessonClose} 
            onConfirm={handleDeleteLessonConfirm} 
            dialogTitle="Delete Lesson"
        />
        <EditDrawer 
            open={expandLesson} 
            title="Edit Lesson"
            onClose={handleEditLessonClose}>
                <EditLesson 
                    activeLesson={activeLesson as ILesson} 
                    onSave={handleEditLessonSave}
                    onClose={handleEditLessonClose}
                />
        </EditDrawer>
    </div>);
};