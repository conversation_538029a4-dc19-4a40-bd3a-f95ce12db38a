import { ICourse } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";

interface ICourseState {
    loading: boolean;
    course: ICourse | null;
    error: string;
}
const initialState: ICourseState = {
    loading: true,
    course: null,
    error: ''
}
// api call
export const fetchCourseInfo = createAsyncThunk(
    "setwin/fetchCourseInfo",
    async (payload: {courseId: string}, { rejectWithValue, dispatch }) => {
        try {
            const fetchCourseInfoRes: ITranportResponse = (await transport.fetch(`course/${payload.courseId}`)) as ITranportResponse;
            return fetchCourseInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while fetching your course details. Please reload your page.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const updateCourseInfo = createAsyncThunk(
    "setwin/updateCourseInfo",
    async ( payload: {formData: ICourse }, { rejectWithValue, dispatch }) => {
        try {
            const { formData } = payload;
            (await transport.put(
                `course/${formData.id}`, formData
            )) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Course updated successfully!',
            }));
            dispatch(fetchCourseInfo({courseId: formData.id as string}));
            return formData;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while updating your course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const deleteCourse = createAsyncThunk(
    "setwin/deleteCourse",
    async ( payload: {courseId: string}, { rejectWithValue, dispatch }) => {
        try {
            const { courseId } = payload;
            const data: ITranportResponse = (await transport.delete(
                `course/${courseId}`
              )) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Course deleted successfully!',
            }))
            return courseId;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while deleting your course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const courseSlice = createSlice({
    name: 'courseState',
    initialState,
    reducers: {
        resetCourses: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [fetchCourseInfo.pending, updateCourseInfo.pending, deleteCourse.pending];
        const rejectedStates = [fetchCourseInfo.rejected, updateCourseInfo.rejected, deleteCourse.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        builder.addCase(fetchCourseInfo.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.course = payload;
        });
        builder.addCase(updateCourseInfo.fulfilled, (state) => {
            state.loading = false;
        });
        builder.addCase(deleteCourse.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.course = null;
        });
    },
});

export const { resetCourses } = courseSlice.actions;

export default courseSlice.reducer;