import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { RootState } from "../../store";
import { IProfileForm, ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";
interface IUserInitialState {
    loading: boolean;
    userProfile: IProfileForm
    error: string;
}
const initialState: IUserInitialState = {
    loading: false,
    error: '',
    userProfile: {
        user_id: undefined,
        first_name: "", 
        last_name: "", 
        email: "",
        role: 'student',
        avatar_url: '',
        settings:   {
            notifications: {
                emailnotifications: true,
                pushnotifications: true,
                courseupdates: true,
            },
            preferences: {
                wishlistEnabled: true,
                reminders: true,
            },
            dashboard: {
                showProgress: true,
                showUpcoming: true,
            },
        }
    }
}
// api call
export const fetchCurrentUser = createAsyncThunk(
    "user/fetchCurrentUser",
    async (arg, { getState, rejectWithValue }) => {
        try {
            const state = getState() as RootState;
            transport.setAuthorization(state.authState.authToken);
            const profileRes: ITranportResponse = (await transport.fetch('profile')) as ITranportResponse;
            const data = profileRes.data
            return Object.keys(data).length ? data : initialState.userProfile;
        } catch(error) {
            return rejectWithValue(error);
        }
    }
);

export const upsertCurrentUser = createAsyncThunk(
    "user/upsertCurrentUser",
    async (payload: {formData: IProfileForm}, { rejectWithValue, dispatch }) => {
        try {
            const { formData } = payload;
            const profileRes: ITranportResponse = formData.user_id ? (await transport.put('profile',formData)) as ITranportResponse : (await transport.post('profile',formData)) as ITranportResponse;
            dispatch(fetchCurrentUser());
            dispatch(addNotification({
                severity: 'success',
                message: 'Profile data updated successfully',
            }))
            return profileRes.data;
        } catch (error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred',
            }))
            return rejectWithValue(error);
        }
    }
);

export const currentUserSlice = createSlice({
    name: 'userState',
    initialState,
    reducers: {
        resetCurrentUser: (state, action) => {
            state.userProfile = initialState.userProfile;
            state.loading = false;
            state.error = '';
        },
        setCurrentUser: (state, action) => {
            state = action.payload;
            return state;
        },
        updateCurrentUser: (state, action) => {
            state = {
                ...state,
                ...action.payload
            }
            return state;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [fetchCurrentUser.pending, upsertCurrentUser.pending];
        const rejectedStates = [fetchCurrentUser.rejected, upsertCurrentUser.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error;
            });
        });
        builder.addCase(fetchCurrentUser.fulfilled, (state, action) => {
            state.loading = false;
            state.userProfile = action.payload;
        });
        builder.addCase(upsertCurrentUser.fulfilled, (state, action) => {
            state.loading = false;
            state.userProfile = action.payload.data;
        });
    },
});

export const {setCurrentUser, updateCurrentUser, resetCurrentUser} = currentUserSlice.actions;

export default currentUserSlice.reducer;