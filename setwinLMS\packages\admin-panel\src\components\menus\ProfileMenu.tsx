import * as React from 'react';
import Box from '@mui/material/Box';
import Avatar from '@mui/material/Avatar';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { IUser } from '../../types/app';
import Link from 'next/link';
import { useRouter } from "next/navigation";
import { useAppDispatch, useAppSelector } from '../../lib/hooks';
import { handleLogout } from '../../lib/features/auth/authSlice';
import { deepOrange } from '@mui/material/colors';
export const ProfileMenu = () => {
  const router = useRouter();
  const { userProfile } = useAppSelector((state: { userState: any; }) => state.userState);
  const dispatch = useAppDispatch();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const logout = async () => {
    dispatch(handleLogout())
    router.push("/");
  }
  const getProfileAvatar = () => {
    return <Box display="flex" alignItems="center">
            <Avatar sx={{ width: 32, height: 32, backgroundColor: deepOrange[500] }} >{userProfile?.first_name?.charAt(0)}</Avatar>
            {/* <Typography variant="body1" style={{ marginLeft: 8 }}>
                {userProfile?.name}
            </Typography> */}
        </Box>;
  }
  return (
    <React.Fragment>
      <Box sx={{ display: 'flex', alignItems: 'center', textAlign: 'center', zIndex: 10 }}>
        <Tooltip title="My Profile">
            <IconButton
                onClick={handleClick}
                size="small"
                sx={{ ml: 4 }}
                className='text-white pointer'
                aria-controls={open ? 'account-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
            >
                {getProfileAvatar()}
            </IconButton>
        </Tooltip>
      </Box>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleClose}>
            <Link href="/profile" onClick={handleClose}>
                {"My Profile"}
            </Link>
        </MenuItem>
        <MenuItem onClick={logout}>
          Logout
        </MenuItem>
      </Menu>
    </React.Fragment>
  );
}