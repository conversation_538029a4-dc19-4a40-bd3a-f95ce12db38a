import { Close } from "@mui/icons-material";
import { Drawer, IconButton, Typography } from "@mui/material";

interface EditDrawerProps {
    open: boolean;
    onClose: () => void;
    title: string;
    children: React.ReactNode;
}
export const EditDrawer:React.FC<EditDrawerProps> = ({ open, onClose, title, children }) => {
    return (
        <Drawer
            anchor="right"
            open={open}
            onClose={onClose}
            sx={{
                '& .MuiDrawer-paper': {
                  width: "50%"
                },
            }}
        >
            <div className="p-4 flex flex-col h-full gap-4">
                <div className="flex justify-between items-center border-b border-custom-border-200">
                    <Typography variant="h5">{title}</Typography>
                    <IconButton onClick={onClose}>
                        <Close />
                    </IconButton>
                </div>
                <div className="flex-grow">
                    {children}
                </div>
            </div>
        </Drawer>
    );
}