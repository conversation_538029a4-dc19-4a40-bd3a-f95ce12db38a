"use client"
import React, { useState } from 'react';
import {
  ArrowLeft,
  GraduationCap,
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { IModule } from '../../types/app';
import { List, ListItem, ListItemText, Collapse, Divider, ListItemButton } from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

interface ResumeSidebarProps {
  modules: IModule[];
  activeModuleId: string;
  activeLessonId: string;
  loadCenterContent: (lessonId: string, moduleId: string) => void
}
export const ResumeSidebar:React.FC<ResumeSidebarProps> = ({ modules, activeLessonId, activeModuleId, loadCenterContent }) => {
  const router = useRouter();
  const [openModules, setOpenModules] = useState<{ [key: string]: boolean }>(() => {
    return modules.reduce((p,c) => {
      p[c.id as string] = true
      return p;
    }, {} as {[key: string]: boolean})
  });

  const handleModuleToggle = (moduleId: string) => {
    setOpenModules(prevState => ({
      ...prevState,
      [moduleId]: !prevState[moduleId],
    }));
  };

  const gotoLesson = (lessonId: string, moduleId: string) => {
    console.log("Navigate to lesson ", lessonId, moduleId);
    loadCenterContent(lessonId, moduleId);
  }
  const activeClass = 'bg-indigo-50 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400';
  return (
    <div className="flex h-full flex-col bg-white dark:bg-gray-900">
      <button
        onClick={() => {
          router.push('/my-learning')
        }}
        className="mb-6 flex items-center gap-2 rounded-lg border px-2 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800"
      >
        <ArrowLeft className="h-4 w-4" />
        Back to My Learning
      </button>
      <List className='cursor-pointer'>
        {/* Top default items */}
        <ListItemButton onClick={() => gotoLesson('', 'objectives')} className={activeModuleId === 'objectives' ? activeClass : ''}>
          <ListItemText primary="Course Objectives"/>
        </ListItemButton>
        <ListItemButton onClick={() => gotoLesson('', 'structure')} className={activeModuleId === 'structure' ? activeClass : ''}>
          <ListItemText primary="Course Structure" />
        </ListItemButton>
        <Divider />
        
        {/* Modules and lessons */}
        {modules.map(module => {
          const isActive = activeModuleId === module.id;
          const moduleClasses = (isActive
                    ? activeClass
                    : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800');
          return <div key={module.id}>
            <ListItemButton onClick={() => gotoLesson('', module.id as string)} className={moduleClasses}>
              <ListItemText primary={module.title} />
              {openModules[module.id as string] ? <ExpandLess onClick={() => handleModuleToggle(module.id as string)} /> : <ExpandMore onClick={() => handleModuleToggle(module.id as string)} />}
            </ListItemButton>
            <Collapse in={openModules[module.id as string]} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {module.lessons.map(lesson => {
                  const lessonClasses = "pl-6 " + 
                  (activeLessonId === lesson.id
                    ? activeClass
                    : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800');          
                  return <ListItemButton key={lesson.id} className={lessonClasses} onClick={() => gotoLesson(lesson.id as string, module.id as string, )}>
                    <ListItemText primary={lesson.title} />
                  </ListItemButton>
                })}
              </List>
            </Collapse>
          </div>
      })}
      </List>
    </div>
  );
}