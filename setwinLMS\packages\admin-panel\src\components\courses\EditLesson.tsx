import { FormControl, FormControlLabel, List, Radio, RadioGroup, TextField, Typography, ListItem, ListItemText, IconButton, duration } from "@mui/material";
import { INPUT_STYLE } from "../../constants/component-variables";
import { RichTextEditor } from "../ui/RichTextEditor";
import { ILesson, IMCQContent } from "../../types/app";
import { useState } from "react";
import { SecondaryButton } from "../ui/buttons/secondary-button";
import { PrimaryButton } from "../ui/buttons";
import { MCQComponent } from "../ui/MCQ";
import { Edit, Delete } from '@mui/icons-material';
import { UpsertMCQDialog } from "../dialog/UpsertMCQDialog";

interface EditLessonProps {
    activeLesson: ILesson;
    onSave: (updatedLesson: ILesson) => void;
    onClose: () => void;
}
export const EditLesson:React.FC<EditLessonProps> = ({activeLesson, onSave, onClose}) => {
    const [formData, setFormData] = useState<ILesson>({
        "title": activeLesson?.title || '',
        "duration": activeLesson?.duration || 0,
        "content": activeLesson?.content || '',
        "lesson_type": activeLesson?.lesson_type || 'text',
        position: activeLesson?.position || 0,
    });
    const [showUpsertMCQDialog, setShowUpsertMCQDialog] = useState(false);
    const [selectedMCQ, setSelectedMCQ] = useState<IMCQContent | null>(null);
    const [mcqs, setMCQs] = useState<IMCQContent[]>(() => {
        if (formData.lesson_type === 'quiz') {
            return JSON.parse(formData.content)
        }
        return []
    });
    const [errors, setErrors] = useState({ title: false, content: false, duration: false });
    const validateForm = () => {
        const updatedErrors: { title: boolean, content: boolean, duration: boolean } = { title: false, content: false, duration: false };
        updatedErrors.title = formData.title.trim() === '';
        updatedErrors.content = formData.content.trim() === '';
        updatedErrors.duration = formData.duration === 0;
        setErrors(updatedErrors);
    
        return formData.title.length > 0 && formData.content.length > 0 && formData.duration !== 0;
    }
    const handleAddNewOption = () => {
        setShowUpsertMCQDialog(true);
        setSelectedMCQ(null);
    }

    const handleEditOption = (mcq: IMCQContent) => {
        setShowUpsertMCQDialog(true);
        setSelectedMCQ(mcq);
    }

    const handleOptionClose = () => {
        setShowUpsertMCQDialog(false);
        setSelectedMCQ(null);
    }

    const handleOptionDelete = (mcq: IMCQContent, idx: number) => {
        setMCQs(mcqs.filter((mcq, i) => i !== idx));
        handleOptionClose();
    }

    const handleOptionSave = (mcq: IMCQContent) => {
        if (selectedMCQ && selectedMCQ.id !== -1) {
            const updatedMcqs = [...mcqs];
            updatedMcqs[selectedMCQ.id] = mcq;
            setMCQs(updatedMcqs);
        } else {
            mcq.id = mcqs.length;
            setMCQs([...mcqs, mcq]);
        }
        handleOptionClose();
    }

    const handleRTEChange = (html: string) => {
        updateFormData("content", html);
    };

    const updateFormData = (key: string, value: string) => {
        setFormData({ ...formData, [key]: value });
    }
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        updateFormData(e.target.name, e.target.value);
    };
    
    const handleSave = () => {
        if (formData.lesson_type === 'quiz') {
            formData.content = JSON.stringify(mcqs)
        }
        if (!validateForm()) {
            return;
        }
        const newFormData: ILesson = {
            ...activeLesson,
            ...formData
        }
        onSave(newFormData);
    }
    return (
        <div className="p-4 flex flex-col h-full gap-4">
            <form className="pt-4 flex flex-col gap-2 flex-grow">
                <TextField
                    label='Title'
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    fullWidth
                    required
                    sx={INPUT_STYLE}
                    variant="filled"
                    error={errors.title}
                    helperText={errors.title ? 'Title is a required field' : ''}
                    inputProps={{ maxLength: 80 }}
                />
                <TextField
                    label='Duration'
                    name="duration"
                    type="number"
                    value={formData.duration}
                    onChange={handleChange}
                    fullWidth
                    required
                    sx={INPUT_STYLE}
                    variant="filled"
                    error={errors.duration}
                    helperText={errors.duration ? 'Duration is a required field' : ''}
                    inputProps={{ maxLength: 80 }}
                />
                <FormControl component="fieldset">
                    <RadioGroup
                        value={formData.lesson_type}
                        onChange={handleChange}
                        name="lesson_type"
                        row
                    >
                    <FormControlLabel value="text" disabled={!!activeLesson?.lesson_type} control={<Radio />} label="Text" />
                    <FormControlLabel value="quiz" disabled={!!activeLesson?.lesson_type} control={<Radio />} label="Quiz" />
                    </RadioGroup>
                </FormControl>
                {formData.lesson_type === 'text' ? <div className='flex flex-col gap-2'>
                    <Typography variant="body2" className={errors.content ? "text-red-600" : "text-gray-600"}>Content :</Typography>
                    <div className= {errors.content ? "border border-solid border-red-600" : "border border-solid border-gray-600"}>
                        <RichTextEditor onChange={handleRTEChange} inputHtml={formData.content} />
                    </div>
                    <Typography variant='body2' className='text-sm text-red-600'>{errors.content ? 'Content is a required field' : ''}</Typography>
                </div> : <div className="flex flex-col gap-2">
                    <div className="flex flex-row justify-end items-center">
                        <SecondaryButton onClick={handleAddNewOption}>
                            Add New Option
                        </SecondaryButton>
                    </div>
                    <List>
                        {mcqs.map((mcq, idx) => {
                            return <ListItem className="flex justify-between items-center border-b border-gray-300 gap-4" key={mcq.id + idx}>
                                <ListItemText primary={mcq.question} />
                                <div className="flex space-x-2">
                                    <IconButton onClick={(evt) => {
                                        evt.stopPropagation();
                                        handleEditOption(mcq);
                                    }}>
                                        <Edit />
                                    </IconButton>
                                    <IconButton onClick={(evt) => {
                                        evt.stopPropagation();
                                        handleOptionDelete(mcq, idx)
                                    }}>
                                        <Delete />
                                    </IconButton>
                                </div>
                            </ListItem>
                        })}
                    </List>
                    <UpsertMCQDialog 
                        open={showUpsertMCQDialog} 
                        onClose={() => setShowUpsertMCQDialog(false)}
                        onSubmit={handleOptionSave}
                        existingData={selectedMCQ as IMCQContent}
                    />
                </div> }
            </form>
            <div className="flex justify-end gap-4 mt-auto">
                <SecondaryButton onClick={handleSave}> Save </SecondaryButton>
                <PrimaryButton onClick={onClose}> Cancel </PrimaryButton>
            </div>
        </div>
    );
}