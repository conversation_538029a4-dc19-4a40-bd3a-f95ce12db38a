import { useState } from "react";
import { ICourse, IEnrollment, IModule } from "../../types/app"
import { ResumeSidebar } from "./ResumeSidebar";
import { Box } from "@mui/material";
import { ResumeCenterPanel } from "./ResumeCenterPanel";
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useAppDispatch } from "../../lib/hooks";
import { updateEnrollmentInfo } from "../../lib/features/courses/enrollmentSlice";

interface IResumeCourse {
    modules: IModule[];
    course: ICourse;
    enrolledCourse: IEnrollment
}
export const ResumeCourse:React.FC<IResumeCourse> = ({ modules, course, enrolledCourse }) => {
    const dispatch = useAppDispatch();
    const [activeModuleIndex, setActiveModuleIndex] = useState(() => {
        if (!enrolledCourse?.lastAccessedModule || ['objectives', 'structure'].includes(enrolledCourse?.lastAccessedModule)) {
            return -1;
        }
        return modules.findIndex(v => v.id === enrolledCourse?.lastAccessedModule);
    });
    const [lastLessonId, setLastLessonId] = useState(() => {
        const lessons = modules[modules.length - 1] && modules[modules.length - 1].lessons || [];
        const idx = lessons.length - 1;
        return lessons && lessons[idx] && lessons[idx].id
    })
    const [activeModuleId, setActiveModuleId] = useState(() => {
        return activeModuleIndex === -1 ? (enrolledCourse?.lastAccessedModule || 'objectives') : modules[activeModuleIndex].id
    });
    const [activeLessonId, setActiveLessonId] = useState(() => {
        const lessons = modules[activeModuleIndex] && modules[activeModuleIndex].lessons || [];
        const idx = lessons.findIndex(v => v.id === enrolledCourse?.lastAccessedLesson);
        return lessons && lessons[idx] && lessons[idx].id
    });
    const goToPrevLesson = () => {
        if (activeModuleId === 'structure') {
            loadCenterContent('', 'objectives')
        } else {
            const lessons = modules[activeModuleIndex].lessons;
            const activeLessonIdx = lessons.findIndex(v => v.id === activeLessonId);
            if (activeLessonIdx === 0) {
                loadCenterContent('', activeModuleId as string);
            } else if (activeModuleIndex === 0 && activeLessonId === '') {
                loadCenterContent('', 'structure')
            } else {
                loadCenterContent(lessons[activeLessonIdx - 1].id as string, activeModuleId as string);
            }
        }
    }

    const goToNextLesson = () => {
        if (activeModuleId === 'objectives') {
            loadCenterContent('', 'structure')
        } else if (activeModuleId === 'structure') {
            const newActiveModuleIndex = 0;
            const newActiveModuleId = (modules[newActiveModuleIndex].id as string);
            setActiveModuleIndex(newActiveModuleIndex);
            loadCenterContent('', newActiveModuleId);
        } else {
            const lessons = modules[activeModuleIndex].lessons;
            const activeLessonIdx = lessons.findIndex(v => v.id === activeLessonId);
            if (activeLessonIdx === lessons.length) {
                const newActiveModuleIndex = (activeModuleIndex + 1)
                const newActiveModuleId = (modules[newActiveModuleIndex].id as string);
                const newLessons = modules[newActiveModuleIndex].lessons
                const newActiveLessonId = (newLessons[0].id as string);
                loadCenterContent(newActiveLessonId, newActiveModuleId);
                setActiveModuleIndex(newActiveModuleIndex);
            } else {
                loadCenterContent(lessons[activeLessonIdx + 1].id as string, activeModuleId as string);
            }
        }
    }
    const loadCenterContent = (lessonId: string, moduleId: string) => {
        setActiveLessonId(lessonId);
        setActiveModuleId(moduleId);
        dispatch(updateEnrollmentInfo({
            formData: {
                ...enrolledCourse,
                lastAccessedLesson: lessonId,
                lastAccessedModule: moduleId
            }
        }))
    }
    return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 gap-4 dark:text-slate-100">
        <ResumeSidebar modules={modules} activeModuleId={activeModuleId as string} activeLessonId={activeLessonId as string} loadCenterContent={loadCenterContent}/>
        <div className="flex flex-1 flex-col overflow-hidden">
            <main className="flex-1 overflow-y-auto">
                <div className="mt-16 p-4 flex flex-col w-full justify-between gap-4">
                    <Box className="bg-white p-4">
                        <ResumeCenterPanel lessonId={activeLessonId as string} moduleId={activeModuleId as string}
                            modules={modules} course={course as ICourse} />
                    </Box>
                    {/* Prev Button */}
                    <div className="flex gap-2 justify-between">
                        <button onClick={goToPrevLesson}
                            disabled={activeModuleId === 'objectives'}
                            className={
                                `mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 ` +
                                (activeModuleId === 'objectives' 
                                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400 dark:border-gray-700'
                                  : 'hover:bg-gray-100 dark:hover:bg-gray-800 border-gray-300 dark:border-gray-700')
                              }>
                            <ArrowLeft className="h-4 w-4" />
                            Prev
                        </button>
                        <button onClick={goToNextLesson}
                            disabled={lastLessonId === activeLessonId}
                            className={
                                `mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 ` +
                                (lastLessonId === activeLessonId 
                                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400 dark:border-gray-700'
                                  : 'hover:bg-gray-100 dark:hover:bg-gray-800 border-gray-300 dark:border-gray-700')
                            }>
                            Next
                            <ArrowRight className="h-4 w-4" />
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>
    );
}