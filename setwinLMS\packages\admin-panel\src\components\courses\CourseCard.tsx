import React from 'react';
import { ArrowRight, CheckCircle, XCircle, Users } from 'lucide-react';
import { ICourse } from '../../types/app';
import { useCurrency } from '../../context/CurrencyContext';

interface CourseCardProps {
  course: ICourse;
  progress?: number;
  enrollmentStatus: 'enrolled' | 'available' | 'full';
  onClick: () => void;
}

export function CourseCard({ course, progress, enrollmentStatus, onClick }: CourseCardProps) {
  const { formatPrice } = useCurrency();

  const getStatusBadge = () => {
    switch (enrollmentStatus) {
      case 'enrolled':
        return (
          <div className="absolute right-4 top-4 flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-700 dark:bg-green-900/50 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            Enrolled
          </div>
        );
      case 'full':
        return (
          <div className="absolute right-4 top-4 flex items-center gap-1 rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-700 dark:bg-red-900/50 dark:text-red-400">
            <XCircle className="h-4 w-4" />
            Course Full
          </div>
        );
      case 'available':
        return (
          <div className="absolute right-4 top-4 flex items-center gap-1 rounded-full bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-400">
            <Users className="h-4 w-4" />
            {/* TODO: Design maxStudents & enrolledCount from store */}
            {(course as any).maxStudents - (course.enrollments || []).length} spots left
          </div>
        );
    }
  };

  return (
    <div 
      className="group relative overflow-hidden rounded-lg bg-white shadow-md transition-all hover:shadow-lg dark:bg-gray-800"
      onClick={onClick}
    >
      {getStatusBadge()}
      <div className="aspect-video w-full overflow-hidden">
        <img
          src={(course as any).thumbnail}
          alt={course.title}
          className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </div>
      <div className="p-4">
        <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
          {course.title}
        </h3>
        <p className="mb-4 text-sm text-gray-500 dark:text-gray-400">
          {course.description}
        </p>
        <div className="mb-4 flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
            {/* TODO: Design Course Duration & Instructor information. */}
          <div>{(course as any).duration}</div>
          <div>{(course as any).instructor}</div>
        </div>
        {progress !== undefined && (
          <div className="mb-4">
            <div className="mb-1 flex items-center justify-between text-sm">
              <span className="font-medium text-indigo-600 dark:text-indigo-400">
                Course Progress
              </span>
              <span>{progress}%</span>
            </div>
            <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
              <div
                className="h-full rounded-full bg-indigo-600 transition-all dark:bg-indigo-400"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}
        <div className="mt-4 flex items-center justify-between">
          <div className="text-lg font-semibold text-indigo-600 dark:text-indigo-400">
            {formatPrice(course.price)}
          </div>
          <button className="rounded-full bg-indigo-600 p-2 text-white opacity-0 transition-opacity group-hover:opacity-100 dark:bg-indigo-500">
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}