@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,400,0,0&display=swap");

@tailwind base;
html {
  scroll-behavior: smooth;
}
@tailwind components;
@tailwind utilities;

@layer components {
  .text-1\.5xl {
    font-size: 1.375rem;
    line-height: 1.875rem;
  }

  .text-2\.5xl {
    font-size: 1.75rem;
    line-height: 2.25rem;
  }
}

@layer base {
  html {
    font-family: "Roboto", sans-serif;
  }

  :root {
    color-scheme: light !important;

    --color-primary-10: 236, 241, 255;
    --color-primary-20: 217, 228, 255;
    --color-primary-30: 197, 214, 255;
    --color-primary-40: 178, 200, 255;
    --color-primary-50: 159, 187, 255;
    --color-primary-60: 140, 173, 255;
    --color-primary-70: 121, 159, 255;
    --color-primary-80: 101, 145, 255;
    --color-primary-90: 82, 132, 255;
    --color-primary-100: 63, 118, 255;
    --color-primary-200: 57, 106, 230;
    --color-primary-300: 50, 94, 204;
    --color-primary-400: 44, 83, 179;
    --color-primary-500: 38, 71, 153;
    --color-primary-600: 32, 59, 128;
    --color-primary-700: 25, 47, 102;
    --color-primary-800: 19, 35, 76;
    --color-primary-900: 13, 24, 51;

    --color-background-100: 255, 255, 255; /* primary bg */
    --color-background-90: 250, 250, 250; /* secondary bg */
    --color-background-80: 245, 245, 245; /* tertiary bg */

    --color-text-100: 23, 23, 23; /* primary text */
    --color-text-200: 58, 58, 58; /* secondary text */
    --color-text-300: 82, 82, 82; /* tertiary text */
    --color-text-400: 163, 163, 163; /* placeholder text */

    --color-border-100: 245, 245, 245; /* subtle border= 1 */
    --color-border-200: 229, 229, 229; /* subtle border- 2 */
    --color-border-300: 212, 212, 212; /* strong border- 1 */
    --color-border-400: 185, 185, 185; /* strong border- 2 */

    --color-shadow-2xs: 0px 0px 1px 0px rgba(23, 23, 23, 0.06),
      0px 1px 2px 0px rgba(23, 23, 23, 0.06), 0px 1px 2px 0px rgba(23, 23, 23, 0.14);
    --color-shadow-xs: 0px 1px 2px 0px rgba(0, 0, 0, 0.16), 0px 2px 4px 0px rgba(16, 24, 40, 0.12),
      0px 1px 8px -1px rgba(16, 24, 40, 0.1);
    --color-shadow-sm: 0px 1px 4px 0px rgba(0, 0, 0, 0.01), 0px 4px 8px 0px rgba(0, 0, 0, 0.02),
      0px 1px 12px 0px rgba(0, 0, 0, 0.12);
    --color-shadow-rg: 0px 3px 6px 0px rgba(0, 0, 0, 0.1), 0px 4px 4px 0px rgba(16, 24, 40, 0.08),
      0px 1px 12px 0px rgba(16, 24, 40, 0.04);
    --color-shadow-md: 0px 4px 8px 0px rgba(0, 0, 0, 0.12), 0px 6px 12px 0px rgba(16, 24, 40, 0.12),
      0px 1px 16px 0px rgba(16, 24, 40, 0.12);
    --color-shadow-lg: 0px 6px 12px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.12),
      0px 1px 24px 0px rgba(16, 24, 40, 0.12);
    --color-shadow-xl: 0px 0px 18px 0px rgba(0, 0, 0, 0.16), 0px 0px 24px 0px rgba(16, 24, 40, 0.16),
      0px 0px 52px 0px rgba(16, 24, 40, 0.16);
    --color-shadow-2xl: 0px 8px 16px 0px rgba(0, 0, 0, 0.12),
      0px 12px 24px 0px rgba(16, 24, 40, 0.12), 0px 1px 32px 0px rgba(16, 24, 40, 0.12);
    --color-shadow-3xl: 0px 12px 24px 0px rgba(0, 0, 0, 0.12), 0px 16px 32px 0px rgba(0, 0, 0, 0.12),
      0px 1px 48px 0px rgba(16, 24, 40, 0.12);

    --color-sidebar-background-100: var(--color-background-100); /* primary sidebar bg */
    --color-sidebar-background-90: var(--color-background-90); /* secondary sidebar bg */
    --color-sidebar-background-80: var(--color-background-80); /* tertiary sidebar bg */

    --color-sidebar-text-100: var(--color-text-100); /* primary sidebar text */
    --color-sidebar-text-200: var(--color-text-200); /* secondary sidebar text */
    --color-sidebar-text-300: var(--color-text-300); /* tertiary sidebar text */
    --color-sidebar-text-400: var(--color-text-400); /* sidebar placeholder text */

    --color-sidebar-border-100: var(--color-border-100); /* subtle sidebar border= 1 */
    --color-sidebar-border-200: var(--color-border-100); /* subtle sidebar border- 2 */
    --color-sidebar-border-300: var(--color-border-100); /* strong sidebar border- 1 */
    --color-sidebar-border-400: var(--color-border-100); /* strong sidebar border- 2 */

    --color-sidebar-shadow-2xs: var(--color-shadow-2xs);
    --color-sidebar-shadow-xs: var(--color-shadow-xs);
    --color-sidebar-shadow-sm: var(--color-shadow-sm);
    --color-sidebar-shadow-rg: var(--color-shadow-rg);
    --color-sidebar-shadow-md: var(--color-shadow-md);
    --color-sidebar-shadow-lg: var(--color-shadow-lg);
    --color-sidebar-shadow-xl: var(--color-shadow-xl);
    --color-sidebar-shadow-2xl: var(--color-shadow-2xl);
    --color-sidebar-shadow-3xl: var(--color-shadow-3xl);
  }

  [data-theme="light"],
  [data-theme="light-contrast"] {
    color-scheme: light !important;

    --color-background-100: 255, 255, 255; /* primary bg */
    --color-background-90: 250, 250, 250; /* secondary bg */
    --color-background-80: 245, 245, 245; /* tertiary bg */
  }

  [data-theme="light"] {
    --color-text-100: 23, 23, 23; /* primary text */
    --color-text-200: 58, 58, 58; /* secondary text */
    --color-text-300: 82, 82, 82; /* tertiary text */
    --color-text-400: 163, 163, 163; /* placeholder text */

    --color-border-100: 245, 245, 245; /* subtle border= 1 */
    --color-border-200: 229, 229, 229; /* subtle border- 2 */
    --color-border-300: 212, 212, 212; /* strong border- 1 */
    --color-border-400: 185, 185, 185; /* strong border- 2 */
  }

  [data-theme="light-contrast"] {
    --color-text-100: 11, 11, 11; /* primary text */
    --color-text-200: 38, 38, 38; /* secondary text */
    --color-text-300: 58, 58, 58; /* tertiary text */
    --color-text-400: 115, 115, 115; /* placeholder text */

    --color-border-100: 34, 34, 34; /* subtle border= 1 */
    --color-border-200: 38, 38, 38; /* subtle border- 2 */
    --color-border-300: 46, 46, 46; /* strong border- 1 */
    --color-border-400: 58, 58, 58; /* strong border- 2 */
  }

  [data-theme="dark"],
  [data-theme="dark-contrast"] {
    color-scheme: dark !important;

    --color-background-100: 7, 7, 7; /* primary bg */
    --color-background-90: 11, 11, 11; /* secondary bg */
    --color-background-80: 23, 23, 23; /* tertiary bg */

    --color-shadow-2xs: 0px 0px 1px 0px rgba(0, 0, 0, 0.15), 0px 1px 3px 0px rgba(0, 0, 0, 0.5);
    --color-shadow-xs: 0px 0px 2px 0px rgba(0, 0, 0, 0.2), 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
    --color-shadow-sm: 0px 0px 4px 0px rgba(0, 0, 0, 0.2), 0px 2px 6px 0px rgba(0, 0, 0, 0.5);
    --color-shadow-rg: 0px 0px 6px 0px rgba(0, 0, 0, 0.2), 0px 4px 6px 0px rgba(0, 0, 0, 0.5);
    --color-shadow-md: 0px 2px 8px 0px rgba(0, 0, 0, 0.2), 0px 4px 8px 0px rgba(0, 0, 0, 0.5);
    --color-shadow-lg: 0px 4px 12px 0px rgba(0, 0, 0, 0.25), 0px 4px 10px 0px rgba(0, 0, 0, 0.55);
    --color-shadow-xl: 0px 0px 14px 0px rgba(0, 0, 0, 0.25), 0px 6px 10px 0px rgba(0, 0, 0, 0.55);
    --color-shadow-2xl: 0px 0px 18px 0px rgba(0, 0, 0, 0.25), 0px 8px 12px 0px rgba(0, 0, 0, 0.6);
    --color-shadow-3xl: 0px 4px 24px 0px rgba(0, 0, 0, 0.3), 0px 12px 40px 0px rgba(0, 0, 0, 0.65);
  }

  [data-theme="dark"] {
    --color-text-100: 229, 229, 229; /* primary text */
    --color-text-200: 163, 163, 163; /* secondary text */
    --color-text-300: 115, 115, 115; /* tertiary text */
    --color-text-400: 82, 82, 82; /* placeholder text */

    --color-border-100: 34, 34, 34; /* subtle border= 1 */
    --color-border-200: 38, 38, 38; /* subtle border- 2 */
    --color-border-300: 46, 46, 46; /* strong border- 1 */
    --color-border-400: 58, 58, 58; /* strong border- 2 */
  }

  [data-theme="dark-contrast"] {
    --color-text-100: 250, 250, 250; /* primary text */
    --color-text-200: 241, 241, 241; /* secondary text */
    --color-text-300: 212, 212, 212; /* tertiary text */
    --color-text-400: 115, 115, 115; /* placeholder text */

    --color-border-100: 245, 245, 245; /* subtle border= 1 */
    --color-border-200: 229, 229, 229; /* subtle border- 2 */
    --color-border-300: 212, 212, 212; /* strong border- 1 */
    --color-border-400: 185, 185, 185; /* strong border- 2 */
  }

  [data-theme="motor-360"] {
    color-scheme: dark !important;

    --color-background-100: 26, 75, 91; /* primary background */
    --color-background-90: 35, 100, 115; /* secondary background */
    --color-background-80: 20, 55, 68; /* tertiary background */
    
    --color-text-100: 255, 255, 255; /* primary text */
    --color-text-200: 235, 235, 235; /* secondary text */
    --color-text-300: 180, 220, 230; /* tertiary text */
    
    --color-border-100: 35, 100, 115; /* primary border */
    --color-border-90: 45, 120, 135; /* secondary border */
    --color-border-80: 25, 60, 75; /* tertiary border */
    
    --color-primary: 0, 170, 255; /* primary */
    --color-secondary: 255, 100, 0; /* secondary */
    --color-tertiary: 255, 215, 0; /* tertiary */
    --color-error: 255, 0, 0; /* error */
    --color-warning: 255, 165, 0; /* warning */
    --color-success: 0, 255, 0; /* success */
  }

  [data-theme="light"],
  [data-theme="dark"],
  [data-theme="light-contrast"],
  [data-theme="dark-contrast"] {
    --color-primary-10: 236, 241, 255;
    --color-primary-20: 217, 228, 255;
    --color-primary-30: 197, 214, 255;
    --color-primary-40: 178, 200, 255;
    --color-primary-50: 159, 187, 255;
    --color-primary-60: 140, 173, 255;
    --color-primary-70: 121, 159, 255;
    --color-primary-80: 101, 145, 255;
    --color-primary-90: 82, 132, 255;
    --color-primary-100: 63, 118, 255;
    --color-primary-200: 57, 106, 230;
    --color-primary-300: 50, 94, 204;
    --color-primary-400: 44, 83, 179;
    --color-primary-500: 38, 71, 153;
    --color-primary-600: 32, 59, 128;
    --color-primary-700: 25, 47, 102;
    --color-primary-800: 19, 35, 76;
    --color-primary-900: 13, 24, 51;

    --color-sidebar-background-100: var(--color-background-100); /* primary sidebar bg */
    --color-sidebar-background-90: var(--color-background-90); /* secondary sidebar bg */
    --color-sidebar-background-80: var(--color-background-80); /* tertiary sidebar bg */

    --color-sidebar-text-100: var(--color-text-100); /* primary sidebar text */
    --color-sidebar-text-200: var(--color-text-200); /* secondary sidebar text */
    --color-sidebar-text-300: var(--color-text-300); /* tertiary sidebar text */
    --color-sidebar-text-400: var(--color-text-400); /* sidebar placeholder text */

    --color-sidebar-border-100: var(--color-border-100); /* subtle sidebar border= 1 */
    --color-sidebar-border-200: var(--color-border-100); /* subtle sidebar border- 2 */
    --color-sidebar-border-300: var(--color-border-100); /* strong sidebar border- 1 */
    --color-sidebar-border-400: var(--color-border-100); /* strong sidebar border- 2 */
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-variant-ligatures: none;
  -webkit-font-variant-ligatures: none;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

body {
  color: rgba(var(--color-text-100));
}

/* scrollbar style */
::-webkit-scrollbar {
  display: none;
}

.horizontal-scroll-enable {
  overflow-x: scroll;
}

.horizontal-scroll-enable::-webkit-scrollbar {
  display: block;
  height: 7px;
  width: 0;
}

.horizontal-scroll-enable::-webkit-scrollbar-track {
  height: 7px;
  background-color: rgba(var(--color-background-100));
}

.horizontal-scroll-enable::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(var(--color-background-80));
}

.vertical-scroll-enable::-webkit-scrollbar {
  display: block;
  width: 5px;
}

.vertical-scroll-enable::-webkit-scrollbar-track {
  width: 5px;
}

.vertical-scroll-enable::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(var(--color-background-90));
}
/* end scrollbar style */

.tags-input-container {
  border: 2px solid #000;
  padding: 0.5em;
  border-radius: 3px;
  width: min(80vw, 600px);
  margin-top: 1em;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5em;
}

.tag-item {
  background-color: rgb(218, 216, 216);
  display: inline-block;
  padding: 0.5em 0.75em;
  border-radius: 20px;
}
.tag-item .close {
  height: 20px;
  width: 20px;
  background-color: rgb(48, 48, 48);
  color: #fff;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-left: 0.5em;
  font-size: 18px;
  cursor: pointer;
}

.tags-input {
  flex-grow: 1;
  padding: 0.5em 0;
  border: none;
  outline: none;
}

/* emoji icon picker */
.conical-gradient {
  background: conic-gradient(
    from 180deg at 50% 50%,
    #ff6b00 0deg,
    #f7ae59 70.5deg,
    #3f76ff 151.12deg,
    #05c3ff 213deg,
    #18914f 289.87deg,
    #f6f172 329.25deg,
    #ff6b00 360deg
  );
}

/* progress bar */
.progress-bar {
  fill: currentColor;
  color: rgba(var(--color-sidebar-background-100));
}

/* lineclamp */
.lineclamp {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

/* popover2 styling */
.bp4-popover2-transition-container {
  z-index: 1 !important;
}

::-webkit-input-placeholder,
::placeholder,
:-ms-input-placeholder {
  color: rgb(var(--color-text-400));
}

input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus, 
input:-webkit-autofill:active{
   -webkit-box-shadow: 0 0 0 30px #ffffff inset !important;
   -webkit-text-fill-color: #000000 !important;
}

.bp4-overlay-content {
  z-index: 555 !important;
}

.disable-scroll {
  overflow: hidden !important;
}



.gsi-material-button {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -webkit-appearance: none;
  background-color: WHITE;
  background-image: none;
  border: 1px solid #747775;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #1f1f1f;
  cursor: pointer;
  font-family: 'Roboto', arial, sans-serif;
  font-size: 14px;
  height: 40px;
  letter-spacing: 0.25px;
  outline: none;
  overflow: hidden;
  padding: 0 12px;
  position: relative;
  text-align: center;
  -webkit-transition: background-color .218s, border-color .218s, box-shadow .218s;
  transition: background-color .218s, border-color .218s, box-shadow .218s;
  vertical-align: middle;
  white-space: nowrap;
  width: auto;
  max-width: 400px;
  min-width: min-content;
}

.gsi-material-button .gsi-material-button-icon {
  height: 20px;
  margin-right: 12px;
  min-width: 20px;
  width: 20px;
}

.gsi-material-button .gsi-material-button-content-wrapper {
  -webkit-align-items: center;
  align-items: center;
  display: flex;
  -webkit-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: nowrap;
  flex-wrap: nowrap;
  height: 100%;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.gsi-material-button .gsi-material-button-contents {
  -webkit-flex-grow: 1;
  flex-grow: 1;
  font-family: 'Roboto', arial, sans-serif;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}

.gsi-material-button .gsi-material-button-state {
  -webkit-transition: opacity .218s;
  transition: opacity .218s;
  bottom: 0;
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.gsi-material-button:disabled {
  cursor: default;
  background-color: #ffffff61;
  border-color: #1f1f1f1f;
}

.gsi-material-button:disabled .gsi-material-button-contents {
  opacity: 38%;
}

.gsi-material-button:disabled .gsi-material-button-icon {
  opacity: 38%;
}

.gsi-material-button:not(:disabled):active .gsi-material-button-state, 
.gsi-material-button:not(:disabled):focus .gsi-material-button-state {
  background-color: #303030;
  opacity: 12%;
}

.gsi-material-button:not(:disabled):hover {
  -webkit-box-shadow: 0 1px 2px 0 rgba(60, 64, 67, .30), 0 1px 3px 1px rgba(60, 64, 67, .15);
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, .30), 0 1px 3px 1px rgba(60, 64, 67, .15);
}

.gsi-material-button:not(:disabled):hover .gsi-material-button-state {
  background-color: #303030;
  opacity: 8%;
}
