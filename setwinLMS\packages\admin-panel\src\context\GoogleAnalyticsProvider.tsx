"use client";
import { pageview } from "../lib/utils/gtag_utils";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useAppSelector } from "../lib/hooks";

export const GoogleAnalyticsProvider = () => {
    const pathname = usePathname();
    const {userProfile} = useAppSelector(state => state.userState);
    useEffect(() => {
        if (typeof window !== 'undefined') {
            pageview(location.href, userProfile)
        }
    }, [pathname]);

    return ""
}