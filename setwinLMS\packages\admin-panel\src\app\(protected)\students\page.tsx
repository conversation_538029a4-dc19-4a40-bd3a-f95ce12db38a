"use client";
import React, { useState, useMemo } from 'react';

import { Edit2, Plus, Trash2, Search, Filter } from 'lucide-react';
import { useAppSelector } from '../../../lib/hooks';
import { IProfileForm } from '../../../types/app';
import { StudentList } from '../../../components/student/StudentList';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

export default function StudentsPage() {
  const navigate = useRouter();
  const { userProfile: user } = useAppSelector(state => state.userState);
  //TODO: Get all students and define helper methods to create/update/delete student via store.
  const { students: initialStudents, addStudent, updateStudent, deleteStudent }: {
    students: IProfileForm[], addStudent: Function, updateStudent: Function, deleteStudent: Function
  } = {
    students: [],
    addStudent: () => {},
    updateStudent: () => {},
    deleteStudent: () => {}
  };
  const [selectedStudent, setSelectedStudent] = useState<typeof initialStudents[0] | null>(null);
  const [studentsList, setStudentsList] = useState(initialStudents);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const isAdmin = user.role === 'admin';
  const [editingStudent, setEditingStudent] = useState<typeof initialStudents[0] | null>(null);

  // Filter students based on search term and filter
  const filteredStudents = useMemo(() => {
    return studentsList.filter(student => {
      const matchesSearch = 
        student.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student?.email?.toLowerCase().includes(searchTerm.toLowerCase());

      if (!matchesSearch) return false;

      //TODO: Get all the enrolled courses of that student
      switch (filter) {
        case 'active':
          return (student as any).enrolledCourses.length > 0;
        case 'inactive':
          return (student as any).enrolledCourses.length === 0;
        default:
          return true;
      }
    });
  }, [studentsList, searchTerm, filter]);

  const handleEditStudent = (student: typeof initialStudents[0]) => {
    setEditingStudent(student);
  };

  const handleSaveStudent = () => {
    if (editingStudent) {
      setStudentsList(studentsList.map(s => 
        s.user_id === editingStudent.user_id ? editingStudent : s
      ));
      setEditingStudent(null);
    }
  };

  const handleDeleteStudent = (studentId: string) => {
    setStudentsList(studentsList.filter(s => s.user_id !== studentId));
    setSelectedStudent(null);
  };

  return (
    <div className="p-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Students</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage and monitor student progress
          </p>
        </div>
        {isAdmin && (
          <button
            onClick={() => navigate.push('/students/new')}
            className="flex items-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            <Plus className="h-5 w-5" />
            Add Student
          </button>
        )}
      </div>

      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search students..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border bg-white pl-10 pr-4 py-2 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
          />
        </div>
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-400" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="rounded-lg border bg-white px-3 py-2 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
          >
            <option value="all">All Students</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      {selectedStudent ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            {editingStudent?.user_id === selectedStudent.user_id ? (
              <div className="flex-1 space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium">First Name</label>
                    <input
                      type="text"
                      value={editingStudent?.first_name}
                      onChange={(e) => setEditingStudent({ ...(editingStudent as any), first_name: e.target.value })}
                      className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">Last Name</label>
                    <input
                      type="text"
                      value={editingStudent?.last_name}
                      onChange={(e) => setEditingStudent({ ...(editingStudent as any), last_name: e.target.value })}
                      className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">Email</label>
                    <input
                      type="email"
                      value={editingStudent?.email}
                      onChange={(e) => setEditingStudent({ ...(editingStudent as any), email: e.target.value })}
                      className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleSaveStudent}
                    className="rounded-lg bg-green-600 px-4 py-2 text-white hover:bg-green-700"
                  >
                    Save Changes
                  </button>
                  <button
                    onClick={() => setEditingStudent(null)}
                    className="rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="flex items-center gap-4">
                  <Image
                    src={(selectedStudent as any).avatar}
                    alt={selectedStudent.first_name + ' ' + selectedStudent.last_name}
                    width={64}
                    height={64}
                    className="h-16 w-16 rounded-full"
                  />
                  <div>
                    <h2 className="text-xl font-semibold">{selectedStudent.first_name + ' ' + selectedStudent.last_name}</h2>
                    <p className="text-gray-500 dark:text-gray-400">
                      {selectedStudent.email}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEditStudent(selectedStudent)}
                    className="rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
                  >
                    Edit Student
                  </button>
                  {isAdmin && (
                    <button
                      onClick={() => handleDeleteStudent(selectedStudent.user_id as string)}
                      className="rounded-lg p-2 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/50"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </>
            )}
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
              <h3 className="mb-4 text-lg font-semibold">Enrolled Courses</h3>
              <div className="space-y-2">
                {(selectedStudent as any).enrolledCourses.map((enrollment: any) => (
                  <div
                    key={enrollment.courseId}
                    className="flex items-center justify-between rounded-lg border p-3 dark:border-gray-700"
                  >
                    <div>
                      <p className="font-medium">
                        {enrollment.courseId}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Progress: {enrollment.progress}%
                      </p>
                    </div>
                    <div className="h-2 w-24 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                      <div
                        className="h-full rounded-full bg-green-500"
                        style={{ width: `${enrollment.progress}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <StudentList
          students={filteredStudents}
          onSelectStudent={setSelectedStudent}
        />
      )}
    </div>
  );
}