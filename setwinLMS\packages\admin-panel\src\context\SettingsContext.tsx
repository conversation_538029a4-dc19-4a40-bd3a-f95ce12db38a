"use client"
import React, { createContext, useContext, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../lib/hooks';
import { upsertCurrentUser } from '../lib/features/currentUser/currentUserSlice';
import { unwrapResult } from '@reduxjs/toolkit';

interface Settings {
  notifications: {
    emailnotifications: boolean;
    pushnotifications: boolean;
    courseupdates: boolean;
  };
  preferences: {
    wishlistEnabled: boolean;
    reminders: boolean;
  };
  dashboard: {
    showProgress: boolean;
    showUpcoming: boolean;
  };
}

interface SettingsContextType {
  settings: Settings;
  updateSettings: (newSettings: Partial<Settings>) => void;
  saveSettings: () => Promise<void>;
  hasUnsavedChanges: boolean;
}

const defaultSettings: Settings = {
  notifications: {
    emailnotifications: true,
    pushnotifications: true,
    courseupdates: true,
  },
  preferences: {
    wishlistEnabled: true,
    reminders: true,
  },
  dashboard: {
    showProgress: true,
    showUpcoming: true,
  },
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();
  const { userProfile } = useAppSelector(state => state.userState)
  const [settings, setSettings] = useState<Settings>(() => {
    if (userProfile?.settings) {
      return typeof userProfile?.settings === 'string' ? JSON.parse(userProfile?.settings) : userProfile?.settings
    } else return defaultSettings
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const updateSettings = (newSettings: Partial<Settings>) => {
    setSettings(current => {
      const updated = {
        ...current,
        ...newSettings,
      };
      setHasUnsavedChanges(true);
      return updated;
    });
  };

  const saveSettings = async () => {
    try {
      dispatch(upsertCurrentUser({
        formData: {
          ...userProfile,
          settings
        }
      }))
      .then(unwrapResult)
      .then(res => {
          if(!res.isError) {
            setHasUnsavedChanges(false);
          }
      })
    } catch (error) {
      return Promise.reject(error);
    }
  };

  return (
    <SettingsContext.Provider value={{ settings, updateSettings, saveSettings, hasUnsavedChanges }}>
      {children}
    </SettingsContext.Provider>
  );
}

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};