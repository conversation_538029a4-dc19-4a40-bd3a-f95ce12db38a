import { ICourse } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";

interface ICoursesState {
    loading: boolean;
    courses: ICourse[];
    error: string;
}
const initialState: ICoursesState = {
    loading: true,
    courses: [],
    error: ''
}
// api call
export const fetchCoursesList = createAsyncThunk(
    "setwin/fetchCoursesList",
    async (_, { rejectWithValue, dispatch }) => {
        try {
            const fetchCourseListRes: ITranportResponse = (await transport.fetch('courses')) as ITranportResponse;
            return fetchCourseListRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while fetching your courses. Please reload your page.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const updateCourseInfo = createAsyncThunk(
    "setwin/updateCourseInfo",
    async ( payload: {formData: ICourse }, { rejectWithValue, dispatch }) => {
        try {
            const { formData } = payload;
            (await transport.put(
                `course/${formData.id}`, formData
            )) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Course updated successfully!',
            }));
            dispatch(fetchCoursesList());
            return formData;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while updating your course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const addCourseInfo = createAsyncThunk(
    "setwin/addCourseInfo",
    async ( payload: {formData: ICourse}, { rejectWithValue, dispatch }) => {
        try {
            const { formData } = payload;
            (await transport.post(
                `course`, formData
            )) as ITranportResponse;
            const message = 'Course added successfully!';
            dispatch(addNotification({
                severity: 'success',
                message,
            }));
            dispatch(fetchCoursesList());
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while adding your course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const deleteCourse = createAsyncThunk(
    "setwin/deleteCourse",
    async ( payload: {courseId: string}, { rejectWithValue, dispatch }) => {
        try {
            const { courseId } = payload;
            const data: ITranportResponse = (await transport.delete(
                `course/${courseId}`
              )) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Course deleted successfully!',
            }))
            return courseId;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while deleting your course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const coursesSlice = createSlice({
    name: 'courseListState',
    initialState,
    reducers: {
        resetCourses: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [fetchCoursesList.pending, updateCourseInfo.pending, addCourseInfo.pending, deleteCourse.pending];
        const rejectedStates = [fetchCoursesList.rejected, updateCourseInfo.rejected, addCourseInfo.rejected, deleteCourse.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        builder.addCase(fetchCoursesList.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.courses = payload;
        });
        builder.addCase(addCourseInfo.fulfilled, (state, { payload }) => {
            state.loading = false;
        });
        builder.addCase(updateCourseInfo.fulfilled, (state) => {
            state.loading = false;
        });
        builder.addCase(deleteCourse.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.courses = state.courses.filter(v => v.id === payload)
        });
    },
});

export const { resetCourses } = coursesSlice.actions;

export default coursesSlice.reducer;