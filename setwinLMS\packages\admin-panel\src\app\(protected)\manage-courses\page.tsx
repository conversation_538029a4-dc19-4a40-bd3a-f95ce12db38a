"use client";
import React, { useEffect, useState } from 'react';
import { Upload, Plus, FileSpreadsheet, Trash2, ArrowRight, Edit2, Link as LinkIcon, HelpCircle, BookOpen } from 'lucide-react';

import { useRouter } from 'next/navigation';
import { useCurrency } from '../../../context/CurrencyContext';
import { useAppDispatch, useAppSelector } from '../../../lib/hooks';
import { ICourse, ILesson, IModule } from '../../../types/app';
import { addCourseInfo, deleteCourse, fetchCoursesList, updateCourseInfo } from '../../../lib/features/courses/coursesSlice';
import { ModuleList } from '../../../components/courses/ModuleList';
import { RichTextEditor } from '../../../components/ui/RichTextEditor';

export default function ManageCourses() {
    const navigate = useRouter();
    const dispatch = useAppDispatch();
    useEffect(() => {
        dispatch(fetchCoursesList());
    }, [])
    const { userProfile } = useAppSelector(state => state.userState);
    const { courses } = useAppSelector(state => state.courseListState)
    const { formatPrice } = useCurrency();
    const [isAddingCourse, setIsAddingCourse] = useState(false);
    const [editingCourse, setEditingCourse] = useState<ICourse | null>(null);
    const [showGuide, setShowGuide] = useState(false);
    const [showModuleEditor, setShowModuleEditor] = useState(false);
    const [selectedModule, setSelectedModule] = useState<IModule | null>(null);
    const [selectedLesson, setSelectedLesson] = useState<ILesson | null>(null);
    const [newCourse, setNewCourse] = useState<Partial<ICourse>>({
      status: 'draft',
      price: 0,
      maxStudents: 20,
      modules: [],
      description: '',
      title: '',
      objectives: ''
    });
  
    const handleViewCourse = (courseId: string) => {
      navigate.push(`/manage-courses/${courseId}`);
    };
  
    const handleEditCourse = (course: ICourse) => {
      setEditingCourse(course);
      setNewCourse(course);
      setIsAddingCourse(true);
    };
  
    const handleSaveCourse = () => {
      if (newCourse.title && newCourse.description) {
        const courseData: ICourse = {
            title: newCourse.title,
            description: newCourse.description,
            duration: (newCourse.duration || 8) as number,
            instructor: newCourse.instructor || userProfile,
            thumbnail: newCourse.thumbnail || 'https://images.unsplash.com/photo-1677442136019-21780ecad995?auto=format&fit=crop&q=80',
            price: newCourse.price || 0,
            maxStudents: newCourse.maxStudents || 20,
            status: newCourse.status || 'draft',
            objectives: newCourse.objectives || '',
            structure: newCourse.structure || ''
        };
  
        if (editingCourse) {
            dispatch(updateCourseInfo({formData: {...courseData, id: newCourse.id }}))
        } else {
            dispatch(addCourseInfo({formData: courseData}))
        }
  
        setNewCourse({
          status: 'draft',
          price: 0,
          maxStudents: 20,
          modules: []
        });
        setIsAddingCourse(false);
        setEditingCourse(null);
      }
    };
  
    const handleAddModule = () => {
        if (!editingCourse && !newCourse) return;
        
        const newModule: IModule = {
            title: 'New Module',
            description: '',
            duration: 10,
            course: (editingCourse || newCourse) as ICourse,
            position: ((editingCourse || newCourse).modules?.length || 0) + 1,
            lessons: []
        };
  
        setNewCourse({
            ...newCourse,
            modules: [...(newCourse.modules || []), newModule]
        });
    };
  
    const handleEditModule = (module: IModule) => {
      setSelectedModule(module);
      setShowModuleEditor(true);
    };
  
    const handleDeleteModule = (moduleId: string) => {
      setNewCourse({
        ...newCourse,
        modules: newCourse.modules?.filter(m => m.id !== moduleId) || []
      });
    };
  
    const handleAddLesson = (moduleId: string) => {
      if (!editingCourse) return;
  
      const activeModule = newCourse.modules?.find(m => m.id === moduleId);
      if (!activeModule) return;
  
      const newLesson: ILesson = {
        id: `lesson-${Date.now()}`,
        title: 'New Lesson',
        duration: 30,
        position: (activeModule.lessons?.length || 0) + 1,
        lesson_type: 'text',
        content: ''
      };
  
      setNewCourse({
        ...newCourse,
        modules: newCourse.modules?.map(m =>
          m.id === moduleId
            ? { ...m, lessons: [...(m.lessons || []), newLesson] }
            : m
        ) || []
      });
    };
  
    const handleEditLesson = (moduleId: string, lesson: ILesson) => {
      setSelectedModule(newCourse.modules?.find(m => m.id === moduleId) || null);
      setSelectedLesson(lesson);
    };
  
    const handleDeleteLesson = (moduleId: string, lessonId: string) => {
      setNewCourse({
        ...newCourse,
        modules: newCourse.modules?.map(m =>
          m.id === moduleId
            ? { ...m, lessons: m.lessons.filter(l => l.id !== lessonId) }
            : m
        ) || []
      });
    };
  
    const ModuleEditor = () => (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          {selectedModule ? 'Edit Module' : 'Add Module'}
        </h3>
        <div>
          <label className="block text-sm font-medium">Title</label>
          <input
            type="text"
            value={selectedModule?.title || ''}
            onChange={(e) => setSelectedModule(prev => prev ? { ...prev, title: e.target.value } : null)}
            className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium">Duration</label>
          <input
            type="text"
            value={selectedModule?.duration || ''}
            onChange={(e) => setSelectedModule(prev => prev ? { ...prev, duration: parseInt(e.target.value, 10) } : null)}
            className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
          />
        </div>
        <div className="flex justify-end gap-2">
          <button
            onClick={() => {
              if (selectedModule) {
                setNewCourse({
                  ...newCourse,
                  modules: newCourse.modules?.map(m =>
                    m.id === selectedModule.id ? selectedModule : m
                  ) || []
                });
              }
              setSelectedModule(null);
              setShowModuleEditor(false);
            }}
            className="rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            Save Module
          </button>
          <button
            onClick={() => {
              setSelectedModule(null);
              setShowModuleEditor(false);
            }}
            className="rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  
    const LessonEditor = () => (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          {selectedLesson ? 'Edit Lesson' : 'Add Lesson'}
        </h3>
        <div>
          <label className="block text-sm font-medium">Title</label>
          <input
            type="text"
            value={selectedLesson?.title || ''}
            onChange={(e) => setSelectedLesson(prev => prev ? { ...prev, title: e.target.value } : null)}
            className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium">Duration</label>
          <input
            type="text"
            value={selectedLesson?.duration || ''}
            onChange={(e) => setSelectedLesson(prev => prev ? { ...prev, duration: parseInt(e.target.value, 10) } : null)}
            className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium">Content</label>
          <textarea
            value={selectedLesson?.content || ''}
            onChange={(e) => setSelectedLesson(prev => prev ? { ...prev, content: e.target.value } : null)}
            className="mt-1 block h-40 w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
          />
        </div>
        <div className="flex justify-end gap-2">
          <button
            onClick={() => {
              if (selectedModule && selectedLesson) {
                setNewCourse({
                  ...newCourse,
                  modules: newCourse.modules?.map(m =>
                    m.id === selectedModule.id
                      ? {
                          ...m,
                          lessons: m.lessons.map(l =>
                            l.id === selectedLesson.id ? selectedLesson : l
                          )
                        }
                      : m
                  ) || []
                });
              }
              setSelectedLesson(null);
            }}
            className="rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            Save Lesson
          </button>
          <button
            onClick={() => setSelectedLesson(null)}
            className="rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>
    );

    return (
        <div className="p-6  dark:text-slate-100">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold">Manage Courses</h1>
              <button
                onClick={() => setShowGuide(!showGuide)}
                className="flex items-center gap-2 rounded-lg border px-3 py-1 text-sm hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
              >
                <HelpCircle className="h-4 w-4" />
                Quick Guide
              </button>
            </div>
            <div className="flex gap-2">
              {/* <label className="flex cursor-pointer items-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700">
                <Upload className="h-5 w-5" />
                <span>Import Courses</span>
                <input
                  type="file"
                  accept=".json,.csv"
                  className="hidden"
                  onChange={(e) => {
                    // Handle file upload
                    console.log('File upload:', e.target.files?.[0]);
                  }}
                />
              </label> */}
              <button
                onClick={() => {
                  setIsAddingCourse(true);
                  setEditingCourse(null);
                  setNewCourse({
                    status: 'draft',
                    price: 0,
                    maxStudents: 20,
                    modules: []
                  });
                }}
                className="flex items-center gap-2 rounded-lg bg-green-600 px-4 py-2 text-white hover:bg-green-700"
              >
                <Plus className="h-5 w-5" />
                Add Course
              </button>
            </div>
          </div>
    
          {showGuide && (
            <div className="mb-6 rounded-lg bg-indigo-50 p-6 dark:bg-indigo-900/50">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-indigo-900 dark:text-indigo-100">
                  Quick Reference Guide
                </h3>
                <button
                  onClick={() => setShowGuide(false)}
                  className="rounded-full p-1 hover:bg-indigo-100 dark:hover:bg-indigo-800"
                >
                  <Trash2 className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-indigo-900 dark:text-indigo-100">
                    Adding a New Course
                  </h4>
                  <ul className="ml-4 list-disc space-y-1 text-sm text-indigo-800 dark:text-indigo-200">
                    <li>Click the &quot;Add Course&quot; button</li>
                    <li>Fill in required course details (title, description, duration)</li>
                    <li>Set pricing and enrollment limits for internal courses</li>
                    <li>Toggle &quot;External Course&quot; for linking to external platforms</li>
                    <li>Provide the external URL for external courses</li>
                  </ul>
                </div>
    
                <div>
                  <h4 className="font-medium text-indigo-900 dark:text-indigo-100">
                    Managing Course Content
                  </h4>
                  <ul className="ml-4 list-disc space-y-1 text-sm text-indigo-800 dark:text-indigo-200">
                    <li>Add modules to organize course content</li>
                    <li>Create lessons within each module</li>
                    <li>Edit module and lesson details</li>
                    <li>Arrange content using drag and drop</li>
                  </ul>
                </div>
    
                <div>
                  <h4 className="font-medium text-indigo-900 dark:text-indigo-100">
                    External Courses
                  </h4>
                  <ul className="ml-4 list-disc space-y-1 text-sm text-indigo-800 dark:text-indigo-200">
                    <li>Enable &quot;External Course&quot; toggle when adding/editing</li>
                    <li>Provide the complete URL to the external course</li>
                    <li>Students will be redirected to the external platform</li>
                    <li>No pricing/enrollment limits needed for external courses</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
    
          {isAddingCourse && (
            <div className="mb-6 rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800 dark:text-slate-100">
              <h2 className="mb-4 text-lg font-semibold">
                {editingCourse ? 'Edit Course' : 'Add New Course'}
              </h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium">Title</label>
                  <input
                    type="text"
                    value={newCourse.title || ''}
                    onChange={(e) => setNewCourse({ ...newCourse, title: e.target.value })}
                    className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium">Duration</label>
                  <input
                    type="text"
                    value={newCourse.duration || ''}
                    onChange={(e) => setNewCourse({ ...newCourse, duration: parseInt(e.target.value, 10) })}
                    className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                    placeholder="e.g., 8 weeks"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium">Description</label>
                  <textarea
                    value={newCourse.description || ''}
                    onChange={(e) => setNewCourse({ ...newCourse, description: e.target.value })}
                    className="mt-1 block h-32 w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium">Objectives</label>
                  <div className='mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700'>
                    <RichTextEditor onChange={(newValue) => setNewCourse({ ...newCourse, objectives: newValue })} inputHtml={newCourse.objectives} />
                  </div>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium">Structure</label>
                  <div className='mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700'>
                    <RichTextEditor onChange={(newValue) => setNewCourse({ ...newCourse, structure: newValue })} inputHtml={newCourse.structure} />
                  </div>
                </div>                
                <div>
                    <label className="block text-sm font-medium">Price</label>
                    <input
                    type="number"
                    value={newCourse.price || 0}
                    onChange={(e) => setNewCourse({ ...newCourse, price: Number(e.target.value) })}
                    className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium">Max Students</label>
                    <input
                    type="number"
                    value={newCourse.maxStudents || 20}
                    onChange={(e) => setNewCourse({ ...newCourse, maxStudents: Number(e.target.value) })}
                    className="mt-1 block w-full rounded-lg border p-2 dark:border-gray-600 dark:bg-gray-700"
                    />
                </div>

                {/* Course Content Management */}
                {/* <div className="md:col-span-2">
                    <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Course Content</h3>
                    <button
                        onClick={handleAddModule}
                        className="flex items-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
                    >
                        <Plus className="h-4 w-4" />
                        Add Module
                    </button>
                    </div>

                    <div className="space-y-4">
                    {newCourse.modules?.map((module) => (
                        <div
                        key={module.id}
                        className="rounded-lg border p-4 dark:border-gray-700"
                        >
                        <div className="mb-4 flex items-center justify-between">
                            <div>
                            <h4 className="font-medium">{module.title}</h4>
                            <p className="text-sm text-gray-500">
                                Duration: {module.duration}
                            </p>
                            </div>
                            <div className="flex gap-2">
                            <button
                                onClick={() => handleEditModule(module)}
                                className="rounded-lg p-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                                <Edit2 className="h-4 w-4" />
                            </button>
                            <button
                                onClick={() => handleDeleteModule(module.id as string)}
                                className="rounded-lg p-2 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/50"
                            >
                                <Trash2 className="h-4 w-4" />
                            </button>
                            </div>
                        </div>

                        <div className="space-y-2">
                            {module.lessons?.map((lesson) => (
                            <div
                                key={lesson.id}
                                className="flex items-center justify-between rounded-lg bg-gray-50 p-2 dark:bg-gray-700"
                            >
                                <div>
                                <span>{lesson.title}</span>
                                <span className="ml-2 text-sm text-gray-500">
                                    {lesson.duration}
                                </span>
                                </div>
                                <div className="flex gap-2">
                                <button
                                    onClick={() => handleEditLesson(module.id as string, lesson)}
                                    className="rounded-lg p-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                    <Edit2 className="h-4 w-4" />
                                </button>
                                <button
                                    onClick={() => handleDeleteLesson(module.id as string, lesson.id as string)}
                                    className="rounded-lg p-2 text-red-600 hover:bg -red-50 dark:text-red-400 dark:hover:bg-red-900/50"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </button>
                                </div>
                            </div>
                            ))}
                            <button
                            onClick={() => handleAddLesson(module.id as string)}
                            className="flex w-full items-center justify-center gap-2 rounded-lg border border-dashed p-2 text-gray-500 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
                            >
                            <Plus className="h-4 w-4" />
                            Add Lesson
                            </button>
                        </div>
                        </div>
                    ))}
                    </div>
                </div> */}
              </div>
              <div className="mt-4 flex justify-end gap-2">
                <button
                  onClick={() => {
                    setIsAddingCourse(false);
                    setEditingCourse(null);
                  }}
                  className="rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveCourse}
                  className="rounded-lg bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
                >
                  {editingCourse ? 'Save Changes' : 'Add Course'}
                </button>
              </div>
            </div>
          )}
    
          {/* {showModuleEditor && <ModuleEditor />}
          {selectedLesson && <LessonEditor />} */}
    
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {courses.map((course) => (
              <div
                key={course.id}
                className="group relative overflow-hidden rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-lg dark:bg-gray-800"
              >
                <div className="aspect-video w-full overflow-hidden rounded-lg">
                  <img
                    src={course.thumbnail}
                    alt={course.title}
                    className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <div className="mt-4">
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold">{course.title}</h3>
                  </div>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {course.description}
                  </p>
                  <div className="mt-4 flex items-center justify-between">
                    <span className="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                      {formatPrice(course.price)}
                    </span>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEditCourse(course)}
                        className="rounded-full bg-indigo-600 p-2 text-white opacity-0 transition-opacity group-hover:opacity-100"
                      >
                        <Edit2 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleViewCourse(course.id as string)}
                        className="rounded-full bg-indigo-600 p-2 text-white opacity-0 transition-opacity group-hover:opacity-100"
                      >
                        <ArrowRight className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => dispatch(deleteCourse({ courseId: course.id as string})) }
                        className="rounded-full bg-red-600 p-2 text-white opacity-0 transition-opacity group-hover:opacity-100"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
}