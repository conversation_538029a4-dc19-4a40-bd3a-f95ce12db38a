import { ILoginForm, IProfileForm, ISignupForm } from "../../types/app";

export const validateEmail = (email: string) => {
    const flags = "gm";
    const pattern = "[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[A-Za-z]{2,}"
    const regexPattern = new RegExp(pattern, flags);
    return email.match(regexPattern);
};

export const validateProfileForm = (formData: IProfileForm) => {
  const messages = [];
  const { first_name, last_name } = formData;
  
  messages.push(...validateName<PERSON>ield(first_name));
  messages.push(...validateNameField(last_name));

  return messages;
} 
export const validatePhoneField = (phoneNumber: string) => {
  const messages = [];

  if (!phoneNumber) {
      messages.push("Please enter your phone number.");
  }

  const countryCodePattern = /^\+\d{1,3}/;
  if (phoneNumber && countryCodePattern.test(phoneNumber)) {
      messages.push("Please enter the phone number without any country codes.");
  }

  if (phoneNumber && phoneNumber.length !== 10) {
      messages.push("Phone number must be 10 digits");
  }

  const nonNumericPattern = /[a-zA-Z]/;
  if (phoneNumber && nonNumericPattern.test(phoneNumber)) {
      messages.push("Phone number must contain only numeric digits.");
  }  
  return messages;
}

export const validateNameField = (name: string) => {
  const messages = [];
  if (!name) {
    messages.push("Name is a required field");
  }
  if (name && name.length > 80) {
    messages.push("Name should not be more than 80 characters.");
  }
  return messages;
}
export const validateEmailField = (email: string) => {
  const messages = [];
  if (!email) {
    messages.push("Email is a required field");
  }
  if (email && !validateEmail(email)) {
    messages.push("Please enter a valid email address for example: <EMAIL>.");
  }
  return messages;
}
export const validateLoginForm = (formData: ILoginForm) => {
  const messages = [];
  const { email, password } = formData;
  
  messages.push(...validateEmailField(email));
  if (!password) {
    messages.push("Password is a required field");
  }

  return messages;
}
export const validateSignupForm = (formData: ISignupForm) => {
    const messages = [];
    const { password, confirmPassword } = formData;

    messages.push(...validateProfileForm(formData as IProfileForm));

    messages.push(...validateLoginForm(formData as ILoginForm));

    if (!confirmPassword) {
        messages.push("Confirm Password is a required field");
    }

    if (password && confirmPassword && password !== confirmPassword) {
        messages.push("Password and Confirm Password are not matching.");
    }
    
    return messages;
}

export const getAccessTokenFromUrl = () => {
  const url = new URL(window.location.href);
  const hash = url.hash.substring(1);
  return hash?.split('&')?.find(v => v.startsWith("access_token"))?.split("=")[1] || "";
}

export const convertToBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};