import { Dialog, DialogActions, DialogContent, DialogTitle, DialogContentText } from "@mui/material";
import { SecondaryButton } from "../ui/buttons/secondary-button";
import { PrimaryButton } from "../ui/buttons";

interface ConfirmDialogProps {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    dialogTitle: string;
    dialogContent?: string;
    submitText?: string;
    cancelText?: string;
}
export const ConfirmDialog:React.FC<ConfirmDialogProps> = ({ open, dialogTitle, dialogContent, submitText, cancelText, onClose, onConfirm }) => {
    return (
        <Dialog open={open} onClose={onClose}>
            <DialogTitle>{dialogTitle}</DialogTitle>
            <DialogContent>
                <DialogContentText>
                    {dialogContent || "Are you sure you want to delete it?"}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <button
                    onClick={onClose}
                    className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2"
                >
                    {cancelText || "Cancel"}
                </button>
                <button
                    onClick={onConfirm}
                    className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 bg-red-600 border-red-600 text-slate-100"
                >
                    {submitText || "Yes, Delete"}
                </button>
            </DialogActions>
        </Dialog>
    );
}