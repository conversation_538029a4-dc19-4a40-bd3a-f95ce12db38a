"use client"
import { TextField, Typography } from "@mui/material";
import { unwrapResult } from "@reduxjs/toolkit";
import { PrimaryButton } from "./ui/buttons";
import { gaCustomEvent } from "../lib/utils/gtag_utils";
import { transport } from "../lib/network/transport";
import { validateLoginForm } from "../lib/utils/app_utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ChangeEvent, useState } from "react";
import { handleLogin } from "../lib/features/auth/authSlice";
import { fetchCurrentUser } from "../lib/features/currentUser/currentUserSlice";
import { useAppDispatch } from "../lib/hooks";
import { ILoginForm, ITranportResponse } from "../types/app";

const INPUT_STYLE = {
  backgroundColor: 'white'
};

export default function Login() {
  const dispatch = useAppDispatch();
  const [formData, setFormData] = useState<ILoginForm>({
    "email": "",
    "password": ""
  });
  const router = useRouter()
  const [errorMessages, setErrorMessages] = useState<string[]>([]);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const signIn = async () => {
    const messages = validateLoginForm(formData);
    if (messages.length > 0) {
      setErrorMessages(messages);
      return;
    }
    gaCustomEvent({
      "action": "btn_click",
      "category": "click",
      "label": "sign_in_btn",
      "value": "user is signing in"
    });
    dispatch(handleLogin({ formData }))
      .then(unwrapResult)
      .then((res: ITranportResponse) => {
        if (!res.isError) {
          dispatch(fetchCurrentUser())
          .then(unwrapResult)
          .then((res: { user_id: any; }) => {
              if (res && res.user_id) {
                  router.push("/courses");
              } else {
                  router.push("/profile");
              }
          })
          .catch(res => {
            console.log(res);
            router.push("/profile");
          });
        } else {
          setErrorMessages(["Could not authenticate user. Please check the credentials and sign-in again."]);
        }
      })
      .catch(() => {
        setErrorMessages(["Could not authenticate user. Please check the credentials and sign-in again."]);
      });
  };

  const signInOAuth = async (provider: string) => {
    gaCustomEvent({
      "action": "btn_click",
      "category": "click",
      "label": "sign_in_with_google_btn",
      "value": "user is signing in with google"
    });
    try {
      const ssoResponse: ITranportResponse = (await transport.post(`sso/${provider}`, {})) as ITranportResponse;
      window.open(ssoResponse.data.redirectUrl) as WindowProxy;
    } catch (error) {
      setErrorMessages(["Could not authenticate user. Please check the credentials and sign-in again."]);
    }
  }
  return <div className="flex flex-col w-full items-center py-16 gap-4">

    <div className="mb-8">
      <Typography variant="h4" className="text-white">Welcome Back!</Typography>
    </div>

    {errorMessages && (
      errorMessages.map((message, idx) => <div className="text-red-500 py-1 md:w-[40%] w-full text-left" key={idx}>
        <p> {message} </p>
      </div>)
    )}
    <div className="flex flex-col gap-2 md:w-[40%] w-full">

      <button className="gsi-material-button" style={{ maxWidth: '100%' }} onClick={() => signInOAuth('google')}>
        <div className="gsi-material-button-state"></div>
        <div className="gsi-material-button-content-wrapper">
          <div className="gsi-material-button-icon">
            <svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" xmlnsXlink="http://www.w3.org/1999/xlink" style={{ display: 'block' }}>
              <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"></path>
              <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"></path>
              <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"></path>
              <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"></path>
              <path fill="none" d="M0 0h48v48H0z"></path>
            </svg>
          </div>
          <span className="gsi-material-button-contents" style={{ fontSize: '16px' }}>Sign in with Google</span>
          <span style={{ display: 'none' }}>Sign in with Google</span>
        </div>
      </button>

    </div>
    <div className="flex md:w-[40%] w-full items-center gap-2 py-6">
      <div className="h-px w-[46%] bg-slate-200"></div>
      OR
      <div className="h-px w-[47%] bg-slate-200"></div>
    </div>
    <form className="flex flex-col gap-2 md:w-[40%] w-full">
      <TextField
        label='Username'
        name="email"
        value={formData.email}
        onChange={handleChange}
        sx={INPUT_STYLE}
        fullWidth
        required
        variant="filled"
      />
      <TextField
        label="Password"
        name="password"
        value={formData.password}
        type="password"
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            signIn();
          }
        }}
        onChange={handleChange}
        sx={INPUT_STYLE}
        fullWidth
        variant="filled"
      />

      <div className="flex items-center justify-between mb-2">
        <a href="/forgot-password" className="text-white hover:underline text-sm">
          Forgot Password?
        </a>
      </div>

      <div className="flex justify-center">
        <PrimaryButton
          variant="contained"
          onClick={signIn}
          className="rounded-md px-4 py-2 mb-2 w-[47%] ml-auto mr-auto"
        >
          Sign In
        </PrimaryButton>
      </div>
    </form>
    <div className="mt-4 text-center">
      {"Don't have an account? "}
      <Link href="/signup" className="underline"> Sign up</Link>
    </div>
  </div>
}

