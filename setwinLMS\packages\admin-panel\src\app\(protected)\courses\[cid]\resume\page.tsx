"use client"
import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../lib/hooks";
import { fetchCourseInfo } from "../../../../../lib/features/courses/courseSlice";
import { fetchModulesByCourse } from "../../../../../lib/features/courses/modulesSlice";
import { fetchEnrollmentsList } from "../../../../../lib/features/courses/enrollmentSlice";
import { Box, CircularProgress, Typography } from "@mui/material";
import { ResumeCourse } from "../../../../../components/courses/ResumeCourse";
import { ICourse, IEnrollment } from "../../../../../types/app";

export default function CourseResumePage({ params }: { params: { cid: string } }) {
    const dispatch = useAppDispatch();
    const {modules, enrolledCourse, course, loading} = useAppSelector(state => ({
        modules: state.modulesState.modules,
        enrolledCourse: state.enrollmentsState.enrollments.find(v => v.course.id === params.cid),
        course: state.courseState.course,
        loading: state.modulesState.loading
    }));

    useEffect(() => {
        dispatch(fetchCourseInfo({ courseId: params.cid }));
        dispatch(fetchModulesByCourse({ courseId: params.cid }));
        dispatch(fetchEnrollmentsList());
    }, []);

    return loading ?  <div className="flex items-center justify-center min-h-screen bg-gray-100">
            <Box className="flex flex-col items-center">
            <CircularProgress size={50} color="primary" />
            <Typography variant="h6" className="mt-4 text-center text-lg text-gray-700">
                Please hold tight, we are resuming your course...
            </Typography>
            </Box>
        </div> : <ResumeCourse modules={modules} enrolledCourse={enrolledCourse as IEnrollment} course={course as ICourse} />;
}