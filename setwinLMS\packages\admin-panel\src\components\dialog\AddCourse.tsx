import * as React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';
import { PrimaryButton } from '../../components/ui/buttons';
import { SecondaryButton } from '../../components/ui/buttons/secondary-button';
import { TextField, Typography } from '@mui/material';
import { ICourseStatus } from '../../types/app';
import { INPUT_STYLE } from '../../constants/component-variables';
import { RichTextEditor } from '../ui/RichTextEditor';
import { useAppDispatch } from '../../lib/hooks';
import { addNotification } from '../../lib/features/notifications/notificationSlice';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export const AddCourseDialog = ({ handleDialogCancel, handleDialogOk }: {handleDialogOk: (formData: any) => void, handleDialogCancel: (e: any) => void}) => {
  const dispatch = useAppDispatch();
  const [formData, setFormData] = React.useState<{"title": string, "description": string, "status": ICourseStatus, "price": number }>({
    "title": '',
    "description": '',
    price: 0,
    status: 'draft'
  });
  const [errors, setErrors] = React.useState({ title: false, description: false });
  const validateForm = () => {
    const updatedErrors: { title: boolean, description: boolean } = { title: false, description: false };
    updatedErrors.title = formData.title.trim() === '';
    updatedErrors.description = formData.description.trim() === '';
    setErrors(updatedErrors);

    return formData.title.length > 0 && formData.description.length > 0;
  }

  const handleRTEChange = (html: string) => {
    updateFormData("description", html);
  };

  const updateFormData = (key: string, value: string) => {
    setFormData({ ...formData, [key]: value });
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    updateFormData(e.target.name, e.target.value);
  };

  return (
    <React.Fragment>
      <Dialog
        open={true}
        TransitionComponent={Transition}
        keepMounted
        onClose={handleDialogCancel}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>
          <Typography className="text-center">Add a new course!</Typography>
        </DialogTitle>
        <DialogContent>
          <form className="pt-4 flex flex-col gap-2">
            <TextField
                label='Title'
                name="title"
                value={formData.title}
                onChange={handleChange}
                fullWidth
                required
                sx={INPUT_STYLE}
                variant="filled"
                error={errors.title}
                helperText={errors.title ? 'Title is a required field' : ''}
                inputProps={{ maxLength: 80 }}
            />
            <div className='flex flex-col gap-2'>
                <Typography variant="body2" className={errors.description ? "text-red-600" : "text-gray-600"}>Description :</Typography>
                <div className= {errors.description ? "border border-solid border-red-600" : "border border-solid border-gray-600"}>
                    <RichTextEditor onChange={handleRTEChange}/>
                </div>
                <Typography variant='body2' className='text-sm text-red-600'>{errors.description ? 'Description is a required field' : ''}</Typography>
            </div>
          </form>
        </DialogContent>
        <DialogActions>
          <SecondaryButton onClick={(e) => {
            setFormData({"title": '', "description": '', price: 0, status: 'draft'});
            handleDialogCancel(e);
          }}>Cancel</SecondaryButton>
          <PrimaryButton onClick={() => {
            if (!validateForm()) {
              dispatch(addNotification({ message: 'Please fill out all the fields', severity: 'error' }));
              return;
            }
            handleDialogOk(formData);
          }}>Create</PrimaryButton>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}
