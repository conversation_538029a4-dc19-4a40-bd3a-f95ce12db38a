.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: rgb(var(--color-text-400));
  pointer-events: none;
  height: 0;
}

.ProseMirror .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: rgb(var(--color-text-400));
  pointer-events: none;
  height: 0;
}

/* Custom image styles */

.ProseMirror img {
  transition: filter 0.1s ease-in-out;

  &:hover {
    cursor: pointer;
    filter: brightness(90%);
  }

  &.ProseMirror-selectednode {
    outline: 3px solid #5abbf7;
    filter: brightness(90%);
  }
}

.ProseMirror-gapcursor:after {
  border-top: 1px solid rgb(var(--color-text-100)) !important;
}

/* Custom TODO list checkboxes – shoutout to this awesome tutorial: https://moderncss.dev/pure-css-custom-checkbox-style/ */

ul[data-type="taskList"] li > label {
  margin-right: 0.2rem;
  user-select: none;
}

@media screen and (max-width: 768px) {
  ul[data-type="taskList"] li > label {
    margin-right: 0.5rem;
  }
}

ul[data-type="taskList"] li > label input[type="checkbox"] {
  -webkit-appearance: none;
  appearance: none;
  background-color: rgb(var(--color-background-100));
  margin: 0;
  cursor: pointer;
  width: 1.2rem;
  height: 1.2rem;
  position: relative;
  border: 2px solid rgb(var(--color-text-100));
  margin-right: 0.3rem;
  display: grid;
  place-content: center;

  &:hover {
    background-color: rgb(var(--color-background-80));
  }

  &:active {
    background-color: rgb(var(--color-background-90));
  }

  &::before {
    content: "";
    width: 0.65em;
    height: 0.65em;
    transform: scale(0);
    transition: 120ms transform ease-in-out;
    box-shadow: inset 1em 1em;
    transform-origin: center;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
  }

  &:checked::before {
    transform: scale(1);
  }
}

ul[data-type="taskList"] li[data-checked="true"] > div > p {
  color: rgb(var(--color-text-200));
  text-decoration: line-through;
  text-decoration-thickness: 2px;
}

/* Overwrite tippy-box original max-width */

.tippy-box {
  max-width: 400px !important;
}

.ProseMirror {
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  -moz-tab-size: 4;
  tab-size: 4;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  outline: none;
  cursor: text;
  line-height: 1.2;
  font-family: inherit;
  font-size: 14px;
  color: black;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  appearance: textfield;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  min-height: 4rem;
  
  & ul, & ol {
    padding-left: 2.5rem;
  }
}

.fadeIn {
  opacity: 1;
  transition: opacity 0.3s ease-in;
}

.fadeOut {
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

.img-placeholder {
  position: relative;
  width: 35%;

  &:before {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 45%;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid rgba(var(--color-text-200));
    border-top-color: rgba(var(--color-text-800));
    animation: spinning 0.6s linear infinite;
  }
}

@keyframes spinning {
  to {
    transform: rotate(360deg);
  }
}

#tiptap-container {
  table {
    border-collapse: collapse;
    table-layout: fixed;
    margin: 0;
    border: 1px solid rgb(var(--color-border-200));
    width: 100%;

    td,
    th {
      min-width: 1em;
      border: 1px solid rgb(var(--color-border-200));
      padding: 10px 15px;
      vertical-align: top;
      box-sizing: border-box;
      position: relative;
      transition: background-color 0.3s ease;

      > * {
        margin-bottom: 0;
      }
    }

    th {
      font-weight: bold;
      text-align: left;
      background-color: rgb(var(--color-primary-100));
    }

    td:hover {
      background-color: rgba(var(--color-primary-300), 0.1);
    }

    .selectedCell:after {
      z-index: 2;
      position: absolute;
      content: "";
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(var(--color-primary-300), 0.1);
      pointer-events: none;
    }

    .column-resize-handle {
      position: absolute;
      right: -2px;
      top: 0;
      bottom: -2px;
      width: 2px;
      background-color: rgb(var(--color-primary-400));
      pointer-events: none;
    }
  }
}

.tableWrapper {
  overflow-x: auto;
}

.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

.ProseMirror table * p {
  padding: 0px 1px;
  margin: 6px 2px;
}

.ProseMirror table * .is-empty::before {
  opacity: 0;
}

.rte-container {
  margin: auto;
  width: 75%;
  border: 1px solid #000;
  border-radius: 2px;
}
.rte-editor {
  padding: 5px;
  border-radius: 5px;
  min-height: 200px;
}
.rte-toolbar {
  top: -130px;
}
.rte-textarea {
  margin-top: 20px;
  resize: none;
  width: 100%;
  border: 1px solid #F1F1F1;
  height: 100px;
}