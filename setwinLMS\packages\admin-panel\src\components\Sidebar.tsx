"use client"
import React from 'react';
import {
  BookOpen,
  Calendar,
  GraduationCap,
  Home,
  BookOpenCheck,
  Settings,
  Users,
  PlusCircle
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useAppSelector } from '../lib/hooks';
import Link from 'next/link';

export function Sidebar() {
  const router = useRouter();
  const pathname = usePathname();
  const { userProfile } = useAppSelector(state => state.userState);

  const isAdmin = userProfile?.role === 'admin';
  const isStudent = userProfile?.role === 'student';
  const isInstructor = userProfile?.role === 'instructor';

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home, show: true },
    { name: 'Courses', href: '/courses', icon: BookOpen, show: true },
    { name: 'Manage Courses', href: '/manage-courses', icon: PlusCircle, show: isAdmin },
    { name: 'My Learning', href: '/my-learning', icon: BookO<PERSON><PERSON>heck, show: isStudent },
    { name: 'Schedule', href: '/schedule', icon: Calendar, show: true },
    { name: 'Students', href: '/students', icon: Users, show: isAdmin },
    { name: 'Certifications', href: '/certifications', icon: GraduationCap, show: isAdmin },
    { name: 'Settings', href: '/settings', icon: Settings, show: true },
  ];

  return (
    <div className="flex h-full w-64 flex-col bg-white dark:bg-gray-900">
      <div 
        onClick={() => router.push('/')}
        className="flex h-16 cursor-pointer items-center px-4 hover:bg-gray-50 dark:hover:bg-gray-800"
      >
        <GraduationCap className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
        <span className="ml-2 text-xl font-bold text-white">AI4ge</span>
      </div>
      <nav className="flex-1 space-y-1 px-2 py-4">
        {navigation.filter(item => item.show).map((item) => {
          const Icon = item.icon;
          const isActive = pathname.includes(item.href);
          const classes = "group flex items-center rounded-lg px-3 py-2 text-sm font-medium " + 
                  (isActive
                    ? 'bg-indigo-50 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400'
                    : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800');
          return (
            <Link
              key={item.name}
              href={item.href}
              className={classes}
            >
              <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
              {item.name}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}