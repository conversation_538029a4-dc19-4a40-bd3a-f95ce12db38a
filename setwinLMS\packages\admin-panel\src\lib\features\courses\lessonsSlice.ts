import { ILesson } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";

interface ILessonsState {
    loading: boolean;
    lessons: ILesson[];
    error: string;
}
const initialState: ILessonsState = {
    loading: true,
    lessons: [],
    error: ''
}
// api call
export const fetchLessonsByModule = createAsyncThunk(
    "setwin/fetchLessonsByModule",
    async (payload: {moduleId: string}, { rejectWithValue, dispatch, getState }) => {
        try {
            const fetchCourseInfoRes: ITranportResponse = (await transport.fetch(`module/${payload.moduleId}/lessons`)) as ITranportResponse;
            return fetchCourseInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while fetching your lessons. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const lessonsSlice = createSlice({
    name: 'lessonsState',
    initialState,
    reducers: {
        resetLessons: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [fetchLessonsByModule.pending];
        const rejectedStates = [fetchLessonsByModule.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        builder.addCase(fetchLessonsByModule.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.lessons = payload;
        });
    },
});

export const { resetLessons } = lessonsSlice.actions;

export default lessonsSlice.reducer;