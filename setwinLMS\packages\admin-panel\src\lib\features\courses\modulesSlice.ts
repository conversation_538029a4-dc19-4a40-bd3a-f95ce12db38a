import { IModule } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";

interface IModulesState {
    loading: boolean;
    modules: IModule[];
    activeCourseId: string;
    error: string;
}
const initialState: IModulesState = {
    loading: true,
    modules: [],
    activeCourseId: '',
    error: ''
}
// api call
export const fetchModulesByCourse = createAsyncThunk(
    "setwin/fetchModulesByCourse",
    async (payload: {courseId: string}, { rejectWithValue, dispatch, getState }) => {
        try {
            const fetchCourseInfoRes: ITranportResponse = (await transport.fetch(`course/${payload.courseId}/modules`)) as ITranportResponse;
            return fetchCourseInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while fetching your modules. Please reload your page.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const modulesSlice = createSlice({
    name: 'modulesState',
    initialState,
    reducers: {
        resetModules: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [fetchModulesByCourse.pending];
        const rejectedStates = [fetchModulesByCourse.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        builder.addCase(fetchModulesByCourse.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.modules = payload;
        });
    },
});

export const { resetModules } = modulesSlice.actions;

export default modulesSlice.reducer;