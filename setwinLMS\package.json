{"name": "root", "private": true, "workspaces": ["packages/*"], "scripts": {"dev:admin": "lerna run dev --scope @setwin/admin-panel --stream", "build:admin": "lerna run build --scope @setwin/admin-panel --stream", "start:admin": "lerna run start --scope @setwin/admin-panel --stream", "start:frontend": "npm run build:admin && npm run start:admin", "dev:backend": "lerna run dev --scope @setwin/backend --stream", "build:backend": "lerna run build --scope @setwin/backend --stream", "start:backend": "lerna run start --scope @setwin/backend --stream", "dev": "lerna run dev --stream", "build": "lerna run build --stream", "start": "npm run start:admin", "lint": "lerna run lint --stream", "test": "lerna run test --stream", "lint:fix": "lerna run lint:fix --stream", "format": "lerna run format --stream"}, "dependencies": {}, "devDependencies": {"lerna": "^8.1.9"}}