import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ISignupForm, ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";
import { resetCurrentUser } from "../currentUser/currentUserSlice";
interface IAuthState {
    loading: boolean;
    isLoggedIn: boolean;
    authToken: string;
    error: string;
}
const initialState: IAuthState = {
    loading: false,
    isLoggedIn: true,
    authToken: '',
    error: ''
}
// api call
export const handleLogin = createAsyncThunk(
    "auth/loginUser",
    async (payload: {formData: any}, { rejectWithValue, dispatch }) => {
        try{
            const { formData } = payload;
            const data: ITranportResponse = (await transport.post('signin', formData)) as ITranportResponse;
            return data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while logging in.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const handleForgotPassword = createAsyncThunk(
    "auth/forgot-password",
    async (payload: {formData: {email: string}}, { rejectWithValue, dispatch }) => {
        try{
            const { formData } = payload;
            const data: ITranportResponse = (await transport.post('forget-password', formData)) as ITranportResponse;


          dispatch(addNotification({
            severity: 'success',
                message: 'Password reset link sent to email.',
            }))
            return data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while sending password reset link.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const handleResetPassword = createAsyncThunk(
    "auth/handleResetPassword",
    async (payload: {formData: {password: string}}, { rejectWithValue, dispatch }) => {
        try{
            const { formData } = payload;
            const data: ITranportResponse = (await transport.put('reset-password', formData)) as ITranportResponse;

            dispatch(addNotification({
                severity: 'success',
                message: 'Password reset successfully.',
            }))
            
            return data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'Unable to reset password.',
            }))
            return rejectWithValue(error);
        }
    }
);


export const handleSignup = createAsyncThunk(
    "auth/signupUser",
    async (payload: {formData: ISignupForm}, { rejectWithValue, dispatch }) => {
        try{
            const { formData } = payload;
            const data: ITranportResponse = (await transport.post('signup', formData)) as ITranportResponse;
            return data.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while logging in.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const handleLogout = createAsyncThunk(
    "auth/logoutUser",
    async (_, { rejectWithValue, dispatch }) => {
        try {
            const data: ITranportResponse = (await transport.post('logout', {})) as ITranportResponse;
            dispatch(resetCurrentUser(null));
            dispatch(resetAuthState(null));
            return data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while logging out.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const isUserLoggedIn = createAsyncThunk(
    "user/isUserLoggedIn",
    async (_, { rejectWithValue }) => {
        try {
            const data: ITranportResponse = (await transport.fetch('profile/is-loggedin')) as ITranportResponse;
            return data.data.isLoggedIn;
        } catch(error) {
            return rejectWithValue(error);
        }
    }
);
export const authSlice = createSlice({
    name: 'authState',
    initialState,
    reducers: {
        resetAuthState: (state, action) => {
            state = initialState;
        },
        signInGoogleSuccess: (state, action) => {
            state.authToken = "Bearer " + action.payload;
            state.isLoggedIn = true;
            state.loading = false;
        },
    },
    extraReducers: (builder) => {
        const pendingStates = [handleLogin.pending, handleLogout.pending, isUserLoggedIn.pending, handleSignup.pending];
        const errorStates = [handleLogin.rejected, handleLogout.rejected, isUserLoggedIn.rejected, handleSignup.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        errorStates.forEach(errorState => {
            builder.addCase(errorState, (state, action) => {
                state.loading = false;
                state.isLoggedIn = false;
                state.authToken = "";
                state.error = action.error.toString();
            });
        });
        builder.addCase(handleLogin.fulfilled, (state, action) => {
          state.loading = false;
          state.isLoggedIn = true;
          state.authToken = "Bearer " + action.payload.data.access_token;
        });
        builder.addCase(handleSignup.fulfilled, (state, action) => {
            state.loading = false;
            state.isLoggedIn = false;
            state.authToken = "";
        });
        builder.addCase(isUserLoggedIn.fulfilled, (state, action) => {
            state.loading = false;
            state.isLoggedIn = action.payload;
        });
        builder.addCase(handleLogout.fulfilled, (state) => {
            state.loading = false;
            state.isLoggedIn = false;
            state.authToken = '';
        });
    },
});

export const { signInGoogleSuccess, resetAuthState } = authSlice.actions;
export default authSlice.reducer;