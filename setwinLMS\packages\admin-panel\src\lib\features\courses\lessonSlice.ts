import { ILesson } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";
import { fetchLessonsByModule } from "./lessonsSlice";

interface ILessonState {
    loading: boolean;
    lesson: ILesson | null;
    activeModuleId: string;
    error: string;
}
const initialState: ILessonState = {
    loading: true,
    lesson: null,
    activeModuleId: '',
    error: ''
}
// api call
export const addLessonInfoToModule = createAsyncThunk(
    "setwin/addLessonInfoToModule",
    async (payload: {moduleId: string, formData: ILesson}, { rejectWithValue, dispatch, getState }) => {
        try {
            const { moduleId } = payload;
            const addLessonInfoRes: ITranportResponse = (await transport.post(`module/${moduleId}/lesson`, payload.formData)) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Lesson added successfully',
            }))
            dispatch(fetchLessonsByModule({ moduleId }));
            return addLessonInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while adding your lesson. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const updateLessonInfoToModule = createAsyncThunk(
    "setwin/updateLessonInfoToModule",
    async (payload: {moduleId: string, formData: ILesson}, { rejectWithValue, dispatch, getState }) => {
        try {
            const {moduleId, formData} = payload
            const addLessonInfoRes: ITranportResponse = (await transport.put(`module/${moduleId}/lesson/${formData.id}`, formData)) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Lesson updated successfully',
            }))
            dispatch(fetchLessonsByModule({ moduleId }));
            return addLessonInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while updating your lesson. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const deleteLessonInfoToModule = createAsyncThunk(
    "setwin/deleteLessonInfoToModule",
    async (payload: {moduleId: string, lessonId: string}, { rejectWithValue, dispatch, getState }) => {
        try {
            const {moduleId, lessonId} = payload
            const deleteLessonInfoRes: ITranportResponse = (await transport.delete(`module/${moduleId}/lesson/${lessonId}`)) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Lesson deleted successfully',
            }))
            dispatch(fetchLessonsByModule({ moduleId }));
            return deleteLessonInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while deleting your lesson. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const lessonSlice = createSlice({
    name: 'lessonState',
    initialState,
    reducers: {
        resetLesson: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [addLessonInfoToModule.pending, updateLessonInfoToModule.pending, deleteLessonInfoToModule.pending];
        const rejectedStates = [addLessonInfoToModule.rejected, updateLessonInfoToModule.rejected, deleteLessonInfoToModule.rejected];
        const fulfilledStates = [addLessonInfoToModule.fulfilled, updateLessonInfoToModule.fulfilled, deleteLessonInfoToModule.fulfilled];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        fulfilledStates.forEach(fulfilledState => {
            builder.addCase(fulfilledState, (state, {payload}) => {
                state.loading = false;
                state.lesson = payload
            });
        });
    },
});

export const { resetLesson } = lessonSlice.actions;

export default lessonSlice.reducer;