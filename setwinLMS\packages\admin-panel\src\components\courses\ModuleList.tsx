import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Card, CardContent } from '@mui/material';
import { DragDropList } from './DragDropList';
import { DropResult } from 'react-beautiful-dnd';
import { ILesson, IModule } from '../../types/app';
import { ConfirmDialog } from '../dialog/DeleteDialog';
import { EditDrawer } from '../ui/EditDrawer';
import { EditModule } from './EditModule';
import { SecondaryButton } from '../ui/buttons/secondary-button';
import { fetchModulesByCourse } from '../../lib/features/courses/modulesSlice';
import { useAppDispatch, useAppSelector } from '../../lib/hooks';
import { addModuleInfoToCourse, deleteModuleInfoToCourse, updateModuleInfoToCourse } from '../../lib/features/courses/moduleSlice';

interface IModuleListProps {
    courseId: string;
}
export const ModuleList: React.FC<IModuleListProps> = ({ courseId }) => {
  const dispatch = useAppDispatch();
  const { modules } = useAppSelector((state) => state.modulesState);
  const [deleteModuleDialogOpen, setDeleteModuleDialogOpen] = useState(false); // Delete module confirmation dialog
  const [expandModule, setExpandModule] = useState<boolean>(false); // Track which module is expanded
  const [selectedModule, setSelectedModule] = useState<IModule | null>(null); // Store selected module for edit

  useEffect(() => {
      if (courseId) {
        dispatch(fetchModulesByCourse({ courseId }));
      }
  }, [courseId]);

  const handleModuleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    if (!destination || destination.index === source.index) return;

    const reorderedModules = Array.from(modules);
    const [removed] = reorderedModules.splice(source.index, 1);
    reorderedModules.splice(destination.index, 0, removed);

    console.log(reorderedModules);
  };

  const handleNewModule = () => {
    setSelectedModule(null);
    setExpandModule(true);
  };
  
  const handleEditModule = (module: IModule | ILesson) => {
    setExpandModule(true);
    setSelectedModule(module as IModule);
  };

  const handleEditModuleClose = () => {
    setExpandModule(false);
    setSelectedModule(null);
  }

  const handleEditModuleSave = (updatedInfo: IModule) => {
    console.log(`Saving module with ID: ${updatedInfo?.id}`);
    if (updatedInfo && updatedInfo.id) {
      dispatch(updateModuleInfoToCourse({ courseId, formData: updatedInfo }));
    } else {
      updatedInfo.position = modules.length;
      dispatch(addModuleInfoToCourse({ courseId, formData: updatedInfo }));
    }
    setExpandModule(false);
    setSelectedModule(null);
  }

  const handleDeleteModule = (module: IModule | ILesson) => {
    console.log(`Delete module with ID: ${module.id}`);
    setSelectedModule(module as IModule);
    setDeleteModuleDialogOpen(true);
  };
  
  const handleDeleteModuleConfirm = () => {
    console.log(`Deleting module with ID: ${selectedModule}`);
    dispatch(deleteModuleInfoToCourse({ courseId, moduleId: (selectedModule as IModule).id as string }));
    setDeleteModuleDialogOpen(false);
    setSelectedModule(null);
  };

  const handleDeleteModuleClose = () => {
    setDeleteModuleDialogOpen(false);
    setSelectedModule(null);
  }

  return (
    <div className="">
      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <div className='flex items-center justify-between'>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Course Content</h2>
          </div>
          <button
            onClick={handleNewModule}
            className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            Add New Module
          </button>
        </div>
        <DragDropList
          items={modules}
          onDragEnd={handleModuleDragEnd}
          onEdit={handleEditModule}
          onDelete={handleDeleteModule}
          isLessonList={false}
          droppableId="module-list"
        />
      </div>

      {/* Edit Module Drawer */}
      <EditDrawer 
        open={expandModule} 
        title="Edit Module"
        onClose={handleEditModuleClose}>
          <EditModule 
            activeModule={selectedModule as IModule} 
            onSave={handleEditModuleSave}
            onClose={handleEditModuleClose}
          />
      </EditDrawer>
      {/* Delete Module Confirmation Dialog */}
      <ConfirmDialog 
        open={deleteModuleDialogOpen} 
        onClose={handleDeleteModuleClose} 
        onConfirm={handleDeleteModuleConfirm} 
        dialogTitle="Delete Module" 
      />
    </div>
  );
};