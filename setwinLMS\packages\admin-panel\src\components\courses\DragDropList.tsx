import React from 'react';
import { List } from '@mui/material';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { SetwinListItem } from './SetwinListItem';
import { ILesson, IModule } from '../../types/app';

interface IDragDropListProps {
    items: IModule[] | ILesson[];
    onDragEnd: (result: DropResult) => void;
    onEdit: (item: IModule | ILesson) => void;
    onDelete: (item: IModule | ILesson) => void;
    droppableId: string;
    isLessonList?: boolean;
}
export const DragDropList: React.FC<IDragDropListProps> = ({ items, onDragEnd, onEdit, onDelete, droppableId, isLessonList = false }) => {
    
    return (
        <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId={droppableId} direction="vertical">
            {(provided) => (
            <List ref={provided.innerRef} {...provided.droppableProps}>
                {items.map((item, index) => (
                    <Draggable key={item.id} draggableId={(item.id as string).toString()} index={index}>
                        {(provided) => (
                            <SetwinListItem 
                                isModule={!isLessonList} 
                                item={item} 
                                index={index}
                                onDelete={onDelete} 
                                onEdit={onEdit} 
                                provided={provided} />  
                        )}
                    </Draggable>
                ))}
                {provided.placeholder}
            </List>
            )}
        </Droppable>
        </DragDropContext>
    );
};
