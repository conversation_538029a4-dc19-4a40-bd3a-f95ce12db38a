import { PrimaryButton } from "../ui/buttons";
import { useRouter } from "next/navigation";
import { ProfileMenu } from "../menus/ProfileMenu";
import Link from "next/link";
import { useAppDispatch, useAppSelector } from "../../lib/hooks";
import { handleLogout } from "../../lib/features/auth/authSlice";

export const AuthButton = ({showMenu}: {showMenu: boolean}) => {
    const router = useRouter();
    // const { notify } = GrowlWrapper();
    const dispatch = useAppDispatch();
    const {isLoggedIn} = useAppSelector((state: { authState: any; }) => state.authState);
    const signIn = () => {
        router.push("/login");
    }

    const logout = async () => {
      dispatch(handleLogout())
      router.push("/");
    }
    const getLoginCmp = () => {
      return <PrimaryButton onClick={signIn} className="z-10">
          <div className="px-5 py-1 text-base">
            Login
          </div>
        </PrimaryButton>
    }
    const getLogoutCmp = () => {
      if (showMenu) {
        return <ProfileMenu />
      }
      return <div className="flex flex-col gap-4 px-4 z-10 text-lg">
              <Link href="/profile">
                  {"My Profile"}
              </Link>
              <div className="flex flex-row gap-2" onClick={logout}>
                {"Logout"}    
              </div>
      </div>
    }
    return <>
      {isLoggedIn ? getLogoutCmp() : getLoginCmp()}
    </>
}