const ROOT_PATH = "/api/";
type REQUEST_METHOD = 'GET' | 'POST' | 'PUT' | 'DELETE';

export class RouteTransport {
    protected accessToken: string = ''
    protected cookie: string = ''
    setAuthorization(accessToken: string) {
        this.accessToken = accessToken;
    }
    setCookie(cookie: string) {
        this.cookie = cookie;
    }
    async fetch(path: string, customHeaders?: Object) {
        const data = await this.fetchWrapper(path, 'GET', '', customHeaders);
        return data;
    }

    async post(path: string, body: object, customHeaders?: Object) {
        const data = await this.fetchWrapper(path, 'POST', JSON.stringify(body), customHeaders);
        return data;
    }

    async put(path: string, body: object, customHeaders?: Object) {
        const data = await this.fetchWrapper(path, 'PUT', JSON.stringify(body), customHeaders);
        return data;
    }

    async delete(path: string, customHeaders?: Object) {
        const data = await this.fetchWrapper(path, 'DELETE', '', customHeaders);
        return data;
    }

    protected async fetchWrapper(url: string, method: REQUEST_METHOD, body: string = '', customHeaders: any = {}) {
        url = url.indexOf('http') === -1 ? `${ROOT_PATH}${url}` : url
        const headers = {
            "Content-Type": "application/json",
            ...customHeaders
        }
        if (this.accessToken) {
            headers.Authorization = this.accessToken
        }
        if (this.cookie) {
            headers.cookie = this.cookie;
        }
        try {
            const reqObj: RequestInit = {method, headers, credentials: 'include', next: { revalidate: 0 }};
            if (body) {
                reqObj.body = body;
            }
            const response = await fetch(url, reqObj);
            
            return response;
        } catch(e) {
            throw e;
        }
        
    }
}

export const routeTransport = new RouteTransport();