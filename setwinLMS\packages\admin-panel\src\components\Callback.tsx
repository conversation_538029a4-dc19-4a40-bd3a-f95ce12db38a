"use client"
import { CircularProgress } from "@mui/material";
import { getAccessTokenFromUrl } from "../lib/utils/app_utils";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { signInGoogleSuccess } from "../lib/features/auth/authSlice";
import { fetchCurrentUser } from "../lib/features/currentUser/currentUserSlice";
import { useAppDispatch } from "../lib/hooks";
import { unwrapResult } from "@reduxjs/toolkit";

export const Callback = () => {
    const dispatch = useAppDispatch();
    const router = useRouter();
    useEffect(() => {
        const accessToken = getAccessTokenFromUrl();
        dispatch(signInGoogleSuccess(accessToken))
        dispatch(fetchCurrentUser())
        .then(unwrapResult)
        .then((res: { user_id: any; }) => {
            if (res && res.user_id) {
                router.push("/courses");
            } else {
                router.push("/profile");
            }
        });
    }, [router, dispatch]);

    return (<div className="flex flex-col w-full pt-16 items-center">
        <div className="flex flex-col items-center justify-center">
            <CircularProgress size={60} className="mb-4" />
            <h2 className="text-lg font-semibold">
                {"Hold tight! We're loading your magic portal... Just a moment!"}
            </h2>
        </div>
    </div>);
}