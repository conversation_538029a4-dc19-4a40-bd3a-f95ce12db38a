import { useEffect, useState } from 'react';
import { fetchModulesByCourse } from '../../lib/features/courses/modulesSlice';
import { useAppDispatch, useAppSelector } from '../../lib/hooks';
import { FileText, ArrowLeft } from 'lucide-react';
import { RichTextEditor } from '../ui/RichTextEditor';
import { ILesson } from '../../types/app';
interface IModuleListProps {
    courseId: string;
}
export const ModuleListReadOnly: React.FC<IModuleListProps> = ({ courseId }) => {
    const dispatch = useAppDispatch();
    const { modules } = useAppSelector((state) => state.modulesState);
    const { userProfile } = useAppSelector(state => state.userState);
    const { enrollments } = useAppSelector(state => state.enrollmentsState);
    useEffect(() => {
        if (courseId) {
        dispatch(fetchModulesByCourse({ courseId }));
        }
    }, [courseId]);
    const [selectedLesson, setSelectedLesson] = useState<{
        moduleId: string;
        lesson: ILesson;
      } | null>(null);
    
    const isAdmin = userProfile?.role === 'admin';
    const isEnrolled = (enrollments || []).find(v => v.student.user_id === userProfile.user_id)

    return <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
            <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-semibold">Course Content</h2>
            </div>
    
            {selectedLesson ? (
                <div className="space-y-4">
                    <button
                    onClick={() => setSelectedLesson(null)}
                    className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700"
                    >
                    <ArrowLeft className="h-4 w-4" />
                    Back to modules
                    </button>
                    <RichTextEditor readonly inputHtml={selectedLesson.lesson.content} /> 
                </div>
            ) : (
            <div className="space-y-4">
                {modules.map((module, index) => (
                    <div
                        key={module.id}
                        className="rounded-lg border p-4 dark:border-gray-700"
                    >
                        <div className="mb-4">
                        <h3 className="text-lg font-medium">
                            Module {index + 1}: {module.title}
                        </h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Duration: {module.duration}
                        </p>
                        </div>

                        {/* Lessons */}
                        <div className="mt-2 space-y-2">
                        {module.lessons.map((lesson, lessonIndex) => (
                            <div
                            key={lesson.id}
                            className="flex items-center justify-between rounded-lg bg-gray-50 p-2 dark:bg-gray-700"
                            >
                            <div>
                                <span>
                                {lessonIndex + 1}. {lesson.title}
                                </span>
                                <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                {lesson.duration}
                                </span>
                            </div>
                            {/* {(isEnrolled || isAdmin) && lesson.content && (
                                <button
                                onClick={() => setSelectedLesson({ moduleId: module.id as string, lesson })}
                                className="rounded-lg p-2 text-indigo-600 hover:bg-indigo-50 dark:text-indigo-400 dark:hover:bg-indigo-900/50"
                                >
                                <FileText className="h-4 w-4" />
                                </button>
                            )} */}
                            </div>
                        ))}
                        </div>
                    </div>
                ))}
            </div>
            )}
        </div>
}