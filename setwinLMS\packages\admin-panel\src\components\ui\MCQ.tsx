import React, { MouseEvent, useState } from 'react';
import { TextField, Button, FormControl, FormLabel, RadioGroup, FormControlLabel, Radio, MenuItem, Select, InputLabel, Container, Grid, Typography, Card, CardContent, IconButton } from '@mui/material';
import { AddCircleOutline, Delete } from '@mui/icons-material';
import { PrimaryButton } from './buttons';
import { addNotification } from '../../lib/features/notifications/notificationSlice';
import { useAppDispatch } from '../../lib/hooks';
import { SecondaryButton } from './buttons/secondary-button';
import { IMCQContent } from '../../types/app';

interface IMCQComponentProps {
  existingData?: IMCQContent;
  onClose: () => void;
  onSubmit: (mcqData: IMCQContent) => void;
}
export const MCQComponent:React.FC<IMCQComponentProps> = ({existingData, onClose, onSubmit}) => {
  // State to manage question, options, and answer
  const [question, setQuestion] = useState(existingData?.question || '');
  const [options, setOptions] = useState(existingData?.options || []);
  const [answer, setAnswer] = useState(existingData?.answer || '');
  const [errors, setErrors] = useState({ options: Array(6).fill(false), question: false, answer: false });
  const dispatch = useAppDispatch();
  const validateForm = () => {
    const updatedErrors: { options: boolean[], question: boolean, answer: boolean } = { options: [], question: false, answer: false };
    updatedErrors.answer = answer.trim() === '';
    updatedErrors.question = question.trim() === '';
    updatedErrors.options = options.map(option => option.trim() === '');
    setErrors(updatedErrors);

    // Return true if no errors
    return !updatedErrors.question && !updatedErrors.answer && !updatedErrors.options.includes(true) && options.length > 1;
  };

  const handleOptionChange = (index: number, value: string) => {
    const updatedOptions = [...options];
    updatedOptions[index] = value;
    setOptions(updatedOptions);
  };

  const handleAddOption = () => {
    setOptions([...options, '']);
  };

  const handleRemoveOption = (index: number) => {
    const updatedOptions = options.filter((_, idx) => idx !== index);
    setOptions(updatedOptions);
  };

  const handleSubmit = (e: MouseEvent) => {
    e.preventDefault();
    if (!validateForm()) {
        dispatch(addNotification({
            severity: 'error',
            message: 'Form has error. Please fill all the fields before submitting the form.',
        }))
        return;
    }
    onSubmit({ ...existingData, question, answer, options } as IMCQContent);
  };

  return (
    <div className='flex flex-col gap-2 mb-4'>
      {/* Question Input */}
      <TextField
          label="Enter your Question"
          fullWidth
          variant="outlined"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          error={errors.question}
          helperText={errors.question ? 'Question is required' : ''}
          className="mb-4"
      />

      {/* Options */}
      {options.map((option, index) => (
          <div key={index} className="flex flex-row gap-2 mb-4">
          <TextField
              label={`Option ${index + 1}`}
              fullWidth
              variant="outlined"
              value={option}
              onChange={(e) => handleOptionChange(index, e.target.value)}
              className="mb-2"
              error={errors.options[index]}
              helperText={errors.options[index] ? 'This field is required' : ''}
          />
          {options.length > 1 && (
              <IconButton onClick={() => handleRemoveOption(index)} size="small">
                  <Delete />
              </IconButton>
          )}
          </div>
      ))}

      {/* Add Option Button */}
      <PrimaryButton
          variant="outlined"
          color="primary"
          onClick={handleAddOption}
          startIcon={<AddCircleOutline />}
          className="w-full mb-4"
      >
          Add Option
      </PrimaryButton>

      {/* Answer Selection */}
      <FormControl fullWidth>
          <InputLabel>Select the correct answer</InputLabel>
          <Select
          value={answer}
          onChange={(e) => setAnswer(e.target.value)}
          label="Select the correct answer"
          error={errors.answer}
          >
          {options.map((option, index) => (
              <MenuItem key={index} value={option}>
              {option}
              </MenuItem>
          ))}
          </Select>
      </FormControl>

      {/* Submit Button */}
      <div className="flex justify-end gap-4 mt-auto">
          <SecondaryButton onClick={handleSubmit}> Submit Question </SecondaryButton>
          <PrimaryButton onClick={onClose}> Cancel </PrimaryButton>
      </div>
    </div>
  );
}
