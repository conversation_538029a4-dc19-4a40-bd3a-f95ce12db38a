import { IModule } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";
import { fetchModulesByCourse } from "./modulesSlice";

interface IModuleState {
    loading: boolean;
    module: IModule | null;
    activeCourseId: string;
    error: string;
}
const initialState: IModuleState = {
    loading: true,
    module: null,
    activeCourseId: '',
    error: ''
}
// api call
export const addModuleInfoToCourse = createAsyncThunk(
    "setwin/addModuleToCourse",
    async (payload: {courseId: string, formData: IModule}, { rejectWithValue, dispatch, getState }) => {
        try {
            const addModuleInfoRes: ITranportResponse = (await transport.post(`course/${payload.courseId}/module`, payload.formData)) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Module added successfully',
            }))
            dispatch(fetchModulesByCourse({ courseId: payload.courseId }));
            return addModuleInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while adding your modules. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const updateModuleInfoToCourse = createAsyncThunk(
    "setwin/updateModuleInfoToCourse",
    async (payload: {courseId: string, formData: IModule}, { rejectWithValue, dispatch, getState }) => {
        try {
            const {courseId, formData} = payload
            const addModuleInfoRes: ITranportResponse = (await transport.put(`course/${courseId}/module/${formData.id}`, formData)) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Module updated successfully',
            }))
            dispatch(fetchModulesByCourse({ courseId: payload.courseId }));
            return addModuleInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while updating your module. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const deleteModuleInfoToCourse = createAsyncThunk(
    "setwin/deleteModuleInfoToCourse",
    async (payload: {courseId: string, moduleId: string}, { rejectWithValue, dispatch, getState }) => {
        try {
            const deleteModuleInfoRes: ITranportResponse = (await transport.delete(`course/${payload.courseId}/module/${payload.moduleId}`)) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Module deleted successfully',
            }))
            dispatch(fetchModulesByCourse({ courseId: payload.courseId }));
            return deleteModuleInfoRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while deleting your module. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const moduleSlice = createSlice({
    name: 'moduleState',
    initialState,
    reducers: {
        resetModule: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [addModuleInfoToCourse.pending, updateModuleInfoToCourse.pending, deleteModuleInfoToCourse.pending];
        const rejectedStates = [addModuleInfoToCourse.rejected, updateModuleInfoToCourse.rejected, deleteModuleInfoToCourse.rejected];
        const fulfilledStates = [addModuleInfoToCourse.fulfilled, updateModuleInfoToCourse.fulfilled, deleteModuleInfoToCourse.fulfilled];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        fulfilledStates.forEach(fulfilledState => {
            builder.addCase(fulfilledState, (state, {payload}) => {
                state.loading = false;
                state.module = payload
            });
        });
    },
});

export const { resetModule } = moduleSlice.actions;

export default moduleSlice.reducer;