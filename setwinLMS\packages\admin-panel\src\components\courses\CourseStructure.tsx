import React from 'react';
import { Typography, List, ListItem } from '@mui/material';

const CourseStructure = () => {
  return (
    <div className="max-w-7xl mx-auto p-4 text-black">
      {/* Curriculum Introduction */}
      <Typography variant="body1" className=" text-lg mb-6">
        This 12-week comprehensive AI curriculum is tailored for beginners, blending theory, hands-on activities, assessments, and resources to create an engaging and effective learning experience.
      </Typography>

      {/* Course Organization Section */}
      <div className="mb-6">
        <Typography variant="h5" className="font-bold  mb-3">
          Course Organization:
        </Typography>
        <Typography variant="body1" className="">
          The course is divided into Modules, typically covered in a week. Each module comprises multiple Lessons, exploring various topics in AI.
        </Typography>
      </div>

      {/* Lesson Structure Section */}
      <div className="mb-6">
        <Typography variant="h5" className="font-bold  mb-3">
          Lesson Structure:
        </Typography>
        <Typography variant="body1" className=" mb-3">
          Each lesson is designed to be completed in 30 minutes and includes:
        </Typography>

        <List className="space-y-4">
          <ListItem>
            <Typography variant="body1" className="">
              <strong>Theory and Concepts (10 minutes):</strong> Introduction to key AI concepts, illustrated with real-world examples.
            </Typography>
          </ListItem>
          <ListItem>
            <Typography variant="body1" className="">
              <strong>Hands-on Activities (10 minutes):</strong> Guided coding exercises, exploratory examples, or follow-along mini-projects to reinforce learning.
            </Typography>
          </ListItem>
          <ListItem>
            <Typography variant="body1" className="">
              <strong>Discussion and Q&A (10 minutes):</strong> Reflection on learnings, brainstorming applications, and addressing questions.
            </Typography>
          </ListItem>
        </List>
      </div>

      {/* Engaging Learning Elements Section */}
      <div className="mb-6">
        <Typography variant="h5" className="font-bold  mb-3">
          Engaging Learning Elements:
        </Typography>
        <Typography variant="body1" className=" mb-3">
          To enhance the learning experience, the course incorporates:
        </Typography>

        <List className="space-y-4">
          <ListItem>
            <Typography variant="body1" className="">
              <strong>Visual aids and interactive demos</strong> to illustrate complex concepts.
            </Typography>
          </ListItem>
          <ListItem>
            <Typography variant="body1" className="">
              <strong>Simplified coding exercises</strong> and hands-on activities.
            </Typography>
          </ListItem>
          <ListItem>
            <Typography variant="body1" className="">
              <strong>Real-world examples</strong> and case studies.
            </Typography>
          </ListItem>
        </List>
      </div>
    </div>
  );
};

export default CourseStructure;
