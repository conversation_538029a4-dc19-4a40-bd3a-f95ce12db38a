"use client";
import { <PERSON><PERSON><PERSON>, Clock, Trophy, XCircle } from 'lucide-react';
import { useRouter } from "next/navigation";
import { useAppSelector } from "../../lib/hooks";
import { IEnrollment, IProfileForm } from "../../types/app";
import { ConfirmDialog } from '../dialog/DeleteDialog';
import { useState } from 'react';
import { CourseCard } from '../courses/CourseCard';

export const MyLearning = () => {
    const navigate = useRouter();
    const [unenrollCourseDialog, setUnenrollCourseDialog] = useState(false);
    const [activeCourseId, setActiveCourseId] = useState('');
    const { userProfile } = useAppSelector(state => state.userState);
    const { courses } = useAppSelector(state => state.courseListState);
    const { enrollments } = useAppSelector(state => state.enrollmentsState)

  
    const inProgressCourses = enrollments.filter(
      enrollment => parseInt(enrollment.progress, 10) < 100
    );
    const completedCourses = enrollments.filter(
      enrollment => parseInt(enrollment.progress, 10) === 100
    );
  
    const handleCourseClick = (courseId: string) => {
      navigate.push(`/courses/${courseId}`);
    };
  
    const handleCloseUnenrollDialog = () => {
        setUnenrollCourseDialog(false);
        setActiveCourseId('');
    }
    const handleUnenroll = (courseId: string) => {
        setUnenrollCourseDialog(true);
        setActiveCourseId(courseId);
    };
  
    const handleUnenrollAction = () => {
        //TODO : Dispatch Enroll Action.
    }
    // Calculate total hours spent
    const totalHoursSpent = enrollments.reduce((acc, enrollment) => {
      const course = courses.find(c => c.id === (enrollment as any).courseId);
      if (!course || !course.modules) return acc;
      
      const totalMinutes = course.modules.reduce((moduleAcc, module) => {
        if (!module.lessons) return moduleAcc;
        return moduleAcc + module.lessons.reduce((lessonAcc, lesson) => {
          const duration = lesson.duration || 0;
          return lessonAcc + duration;
        }, 0);
      }, 0);
  
      return acc + totalMinutes;
    }, 0);
  
    return (
      <div className="p-6 dark:text-slate-100">
        <div className="mb-8">
          <h1 className="text-2xl font-bold">My Learning</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Track your progress and continue learning
          </p>
        </div>
  
        {/* Learning Stats */}
        <div className="mb-8 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div className="flex items-center gap-4 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
            <div className="rounded-lg bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
              <BookOpen className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Courses</p>
              <p className="text-2xl font-bold">{inProgressCourses.length}</p>
            </div>
          </div>
          <div className="flex items-center gap-4 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
            <div className="rounded-lg bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
              <Trophy className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
              <p className="text-2xl font-bold">{completedCourses.length}</p>
            </div>
          </div>
          <div className="flex items-center gap-4 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
            <div className="rounded-lg bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
              <Clock className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Hours Spent</p>
              <p className="text-2xl font-bold">
                {Math.round(totalHoursSpent / 60)}
              </p>
            </div>
          </div>
        </div>
  
        {/* In Progress Courses */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold">In Progress</h2>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {inProgressCourses.map(enrollment => {
              const course = courses.find(c => c.id === (enrollment as IEnrollment).course.id);
              return course && (
                <div key={(enrollment as any).courseId} className="group relative">
                  <CourseCard
                    course={course}
                    progress={parseInt(enrollment.progress, 10)}
                    enrollmentStatus="enrolled"
                    onClick={() => handleCourseClick(course.id as string)}
                  />
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleUnenroll(course.id as string);
                    }}
                    className="absolute right-2 top-2 rounded-full bg-red-600 p-2 text-white opacity-0 transition-opacity hover:bg-red-700 group-hover:opacity-100"
                    title="Unenroll from course"
                  >
                    <XCircle className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
          {inProgressCourses.length === 0 && (
            <div className="flex h-40 items-center justify-center rounded-lg border border-dashed text-gray-500 dark:border-gray-700">
              No courses in progress
            </div>
          )}
        </div>
  
        {/* Completed Courses */}
        {completedCourses.length > 0 && (
          <div>
            <h2 className="mb-4 text-xl font-semibold">Completed</h2>
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {completedCourses.map(enrollment => {
                const course = courses.find(c => c.id === (enrollment as any).courseId);
                return course && (
                  <div key={(enrollment as any).courseId} className="group relative">
                    <CourseCard
                      course={course}
                      progress={parseInt(enrollment.progress, 10)}
                      enrollmentStatus="enrolled"
                      onClick={() => handleCourseClick(course.id as string)}
                    />
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUnenroll(course.id as string);
                      }}
                      className="absolute right-2 top-2 rounded-full bg-red-600 p-2 text-white opacity-0 transition-opacity hover:bg-red-700 group-hover:opacity-100"
                      title="Unenroll from course"
                    >
                      <XCircle className="h-4 w-4" />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        <ConfirmDialog
            open={unenrollCourseDialog} 
            onClose={handleCloseUnenrollDialog} 
            onConfirm={handleUnenrollAction} 
            dialogTitle="Unenroll Course"
            dialogContent='Are you sure you want to unenroll from this course? Your progress will be lost.'
            submitText='Yes, Unenroll'
            />
      </div>
    );
}