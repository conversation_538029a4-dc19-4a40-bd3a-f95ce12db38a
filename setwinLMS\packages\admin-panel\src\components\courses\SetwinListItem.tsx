import { useState } from "react";
import { ListItem, ListItemText, IconButton } from '@mui/material';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { DraggableProvided } from 'react-beautiful-dnd';
import { Edit, Delete } from '@mui/icons-material';
import { LessonList } from './LessonList';
import { ILesson, IModule } from "../../types/app";

interface ListItemProps {
    isModule: boolean;
    index: number;
    item: IModule | ILesson;
    onEdit: (item: IModule | ILesson) => void;
    onDelete: (item: IModule | ILesson) => void;
    provided: DraggableProvided;
}

export const SetwinListItem:React.FC<ListItemProps> = ({ isModule, index, item, onDelete, onEdit, provided}) => {
    const [expandModule, setExpandModule] = useState(false); 
    return (<>
            <ListItem
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                onClick={() => isModule && setExpandModule(!expandModule)}
            >
                <div className="w-full flex justify-between items-center border-b border-gray-300 gap-4">
                    <div className="flex gap-2 items-center">
                        <DragIndicatorIcon />
                        {isModule ? <div className="mb-4">
                            <h3 className="text-lg font-medium">
                            Module {index + 1}: {item.title}
                            </h3>
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Duration: {item.duration}
                            </p>
                        </div> : <div>
                                <span>
                                    {index + 1}. {item.title}
                                </span>
                                <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                    {item.duration}
                                </span>
                                </div>}
                    </div>
                    <div className="flex">
                        <IconButton onClick={(evt) => {
                            evt.stopPropagation();
                            onEdit(item)
                        }}>
                            <Edit className="dark:text-slate-100"/>
                        </IconButton>
                        <IconButton onClick={(evt) => {
                            onDelete(item)
                            evt.stopPropagation();
                        }}>
                            <Delete className="dark:text-slate-100"/>
                        </IconButton>
                    </div>
                </div>
            </ListItem>
            {isModule && expandModule && <LessonList moduleId={item.id as string} />}
        </>
    );
}