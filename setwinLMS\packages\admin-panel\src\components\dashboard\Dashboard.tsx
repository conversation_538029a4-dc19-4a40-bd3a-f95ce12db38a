"use client"
import React, { useEffect, useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { <PERSON><PERSON><PERSON>, Users, Award } from 'lucide-react';
import { transport } from '../../lib/network/transport';
import { ITranportResponse } from '../../types/app';
import { useAppDispatch } from '../../lib/hooks';
import { addNotification } from '../../lib/features/notifications/notificationSlice';

interface IDashboardData {
  totalStudents: number;
  totalCourses: number;
  averageCompletion: number;
  courseEnrollments: {name: string; students: number}[]
}
export const Dashboard = () => {
  const dispatch = useAppDispatch()
  const [data, setData] = useState<IDashboardData>({
      totalStudents: 0, 
      totalCourses: 0, 
      averageCompletion: 0, 
      courseEnrollments: []
  })

  useEffect(() => {
    async function fetchStats() {
      try {
        const statsRes = (await transport.fetch('get-admin-stats')) as ITranportResponse;
        if (statsRes.isError) {
          dispatch(addNotification({ message: statsRes.error.error, severity: 'error'}))
          return;
        }
        setData(statsRes.data);
      } catch(e) {
        dispatch(addNotification({ message: (e as ITranportResponse).error.error, severity: 'error'}))
      }
    }
    fetchStats();
  }, [])

  return (
    <div className="space-y-6 p-6 dark:text-slate-100">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1620712943543-bcc4688e7485?auto=format&fit=crop&q=80"
            alt="AI Technology"
            className="h-full w-full object-cover opacity-20"
          />
        </div>
        <div className="relative px-8 py-12 sm:px-12">
          <h1 className="text-3xl font-bold text-white sm:text-4xl">Welcome to Your Dashboard</h1>
          <p className="mt-2 max-w-2xl text-lg text-indigo-100">
            {"Track your learning platform\'s performance and student engagement in real-time."}
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="flex items-center gap-4 rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="rounded-full bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
            <BookOpen className="h-6 w-6" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Courses</p>
            <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{data.totalCourses}</p>
          </div>
        </div>
        <div className="flex items-center gap-4 rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="rounded-full bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
            <Users className="h-6 w-6" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
            <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{data.totalStudents}</p>
          </div>
        </div>
        <div className="flex items-center gap-4 rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="rounded-full bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
            <Award className="h-6 w-6" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
            <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{data.averageCompletion}%</p>
          </div>
        </div>
      </div>

      {/* Course Enrollment Chart */}
      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <h2 className="mb-4 text-lg font-semibold">Course Enrollments</h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.courseEnrollments}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name"
                tick={{ fontSize: 12 }}
                interval={0}
                angle={-45}
                textAnchor="end"
                height={100}
              />
              <YAxis />
              <Tooltip />
              <Bar dataKey="students" fill="#6366f1" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}