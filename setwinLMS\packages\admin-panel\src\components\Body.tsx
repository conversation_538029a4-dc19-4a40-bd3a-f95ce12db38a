"use client";
import { <PERSON><PERSON>, Typography, Container, <PERSON><PERSON>, <PERSON>, CardContent, Divider } from '@mui/material';
import { Book, CheckCircle, PersonAdd } from '@mui/icons-material'; // Using MUI icons

export const Body = () => {

    return (
        <div className="flex flex-col justify-top items-center w-full gap-4" style={{ minHeight: "calc(85vh - 200px)"}}>
            {/* <Description /> */}
            <div className="bg-gray-50 text-gray-800">

            {/* Hero Section */}
            <section className="bg-blue-600 text-white py-16">
                <Container maxWidth="lg" className="text-center">
                <Typography variant="h2" className="font-extrabold text-4xl md:text-5xl mb-4">
                    Create, Manage, and Share Your Own Courses
                </Typography>
                <Typography variant="h6" className="text-lg mb-8">
                    Our platform allows you to design your own learning experience. Build courses, modules, lessons, and quizzes—all in one place!
                </Typography>
                <Button 
                    variant="contained" 
                    color="secondary" 
                    size="large"
                    href="#how-it-works"
                    className="px-8 py-3 text-lg"
                >
                    Learn More
                </Button>
                </Container>
            </section>

            {/* How It Works Section */}
            <section id="how-it-works" className="py-16 bg-white">
                <Container maxWidth="lg" className="text-center">
                <Typography variant="h4" className="font-semibold text-3xl mb-6">
                    How It Works
                </Typography>
                <Grid container spacing={6}>
                    <Grid item xs={12} sm={4}>
                    <Card className="shadow-lg rounded-lg p-6 bg-gray-100">
                        <Book className="text-4xl text-blue-600 mb-4" />
                        <Typography variant="h5" className="font-semibold mb-4">
                        Create Your Course
                        </Typography>
                        <Typography>
                        Easily build your course content by adding modules, lessons, and quizzes. Tailor the course structure to meet your needs.
                        </Typography>
                    </Card>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                    <Card className="shadow-lg rounded-lg p-6 bg-gray-100">
                        <CheckCircle className="text-4xl text-green-600 mb-4" />
                        <Typography variant="h5" className="font-semibold mb-4">
                        Add Lessons & Quizzes
                        </Typography>
                        <Typography>
                        Each module can have multiple lessons and a quiz. Provide comprehensive content with assessments to test knowledge.
                        </Typography>
                    </Card>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                    <Card className="shadow-lg rounded-lg p-6 bg-gray-100">
                        <PersonAdd className="text-4xl text-purple-600 mb-4" />
                        <Typography variant="h5" className="font-semibold mb-4">
                        Share with Learners
                        </Typography>
                        <Typography>
                        Once your course is ready, share it with learners and track their progress, performance, and feedback.
                        </Typography>
                    </Card>
                    </Grid>
                </Grid>
                </Container>
            </section>

            {/* Key Features Section */}
            <section className="py-16 bg-gray-100">
                <Container maxWidth="lg">
                <Typography variant="h4" className="font-semibold text-3xl text-center mb-8">
                    Key Features
                </Typography>
                <Grid container spacing={6}>
                    <Grid item xs={12} md={6}>
                    <Card className="shadow-lg rounded-lg p-6 bg-white">
                        <Typography variant="h5" className="font-semibold text-blue-600 mb-4">
                        Easy-to-Use Course Builder
                        </Typography>
                        <Typography>
                        Our drag-and-drop interface makes creating courses as simple as it gets. Organize content easily and without hassle.
                        </Typography>
                    </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                    <Card className="shadow-lg rounded-lg p-6 bg-white">
                        <Typography variant="h5" className="font-semibold text-blue-600 mb-4">
                        Multi-Module Structure
                        </Typography>
                        <Typography>
                        Add as many modules and lessons as needed, with quizzes to help reinforce learning. Your course, your structure!
                        </Typography>
                    </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                    <Card className="shadow-lg rounded-lg p-6 bg-white">
                        <Typography variant="h5" className="font-semibold text-blue-600 mb-4">
                        Real-Time Progress Tracking
                        </Typography>
                        <Typography>
                            Track your learners progress, performance, and completion rates, providing insights into their learning journey.
                        </Typography>
                    </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                    <Card className="shadow-lg rounded-lg p-6 bg-white">
                        <Typography variant="h5" className="font-semibold text-blue-600 mb-4">
                        Share & Collaborate
                        </Typography>
                        <Typography>
                        Share your courses with other users and collaborate on content creation. Invite students to enroll and learn.
                        </Typography>
                    </Card>
                    </Grid>
                </Grid>
                </Container>
            </section>

            {/* Call-to-Action Section */}
            <section className="bg-blue-600 text-white py-16">
                <Container maxWidth="lg" className="text-center">
                <Typography variant="h4" className="font-semibold text-3xl mb-4">
                    Ready to Start Creating Your Course?
                </Typography>
                <Typography variant="h6" className="mb-6">
                    Sign up today and begin creating your own courses. Build engaging learning experiences and share them with the world!
                </Typography>
                <Button 
                    variant="contained" 
                    color="secondary" 
                    size="large" 
                    href="/signup"
                    className="px-8 py-3 text-lg"
                >
                    Get Started Now
                </Button>
                </Container>
            </section>

            </div>
        </div>
    )
}