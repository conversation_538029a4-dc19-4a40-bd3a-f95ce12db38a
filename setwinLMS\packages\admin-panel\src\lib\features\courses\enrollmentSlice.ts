import { IEnrollment } from "../../../types/app";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { transport } from "../../network/transport";
import { ITranportResponse } from "../../../types/app";
import { addNotification } from "../notifications/notificationSlice";

interface IEnrollmentsState {
    loading: boolean;
    enrollments: IEnrollment[];
    error: string;
}
const initialState: IEnrollmentsState = {
    loading: true,
    enrollments: [],
    error: ''
}
// api call
export const fetchEnrollmentsList = createAsyncThunk(
    "setwin/fetchEnrollmentsList",
    async (_, { rejectWithValue, dispatch }) => {
        try {
            const fetchEnrollmentsListRes: ITranportResponse = (await transport.fetch('enrollments')) as ITranportResponse;
            return fetchEnrollmentsListRes.data;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while fetching your enrollments. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const updateEnrollmentInfo = createAsyncThunk(
    "setwin/updateEnrollmentInfo",
    async ( payload: {formData: IEnrollment }, { rejectWithValue, dispatch }) => {
        try {
            const { formData } = payload;
            (await transport.put(
                `enrollments/${formData.id}`, formData
            )) as ITranportResponse;
            dispatch(fetchEnrollmentsList());
            return formData;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while updating your enrolled course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const addEnrollmentInfo = createAsyncThunk(
    "setwin/addEnrollmentInfo",
    async ( payload: {formData: IEnrollment}, { rejectWithValue, dispatch }) => {
        try {
            const { formData } = payload;
            (await transport.post(
                `enrollments`, formData
            )) as ITranportResponse;
            const message = 'Thank you, for enrolling to the course.';
            dispatch(addNotification({
                severity: 'success',
                message,
            }));
            dispatch(fetchEnrollmentsList());
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while enrolleing to the course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const unenrollCourse = createAsyncThunk(
    "setwin/unenrollCourse",
    async ( payload: {enrollmentId: string}, { rejectWithValue, dispatch }) => {
        try {
            const { enrollmentId } = payload;
            const data: ITranportResponse = (await transport.delete(
                `unenroll/${enrollmentId}`
              )) as ITranportResponse;
            dispatch(addNotification({
                severity: 'success',
                message: 'Unenrolled successfully!',
            }))
            return enrollmentId;
        } catch(error) {
            dispatch(addNotification({
                severity: 'error',
                message: (error as ITranportResponse).error?.message || 'An error occurred while unenrolling your course. Please try again later.',
            }))
            return rejectWithValue(error);
        }
    }
);

export const enrollmentsSlice = createSlice({
    name: 'enrollmentsState',
    initialState,
    reducers: {
        resetEnrollments: (state, action) => {
            state = initialState;
        }
    },
    extraReducers: (builder) => {
        const pendingStates = [fetchEnrollmentsList.pending, updateEnrollmentInfo.pending, addEnrollmentInfo.pending, unenrollCourse.pending];
        const rejectedStates = [fetchEnrollmentsList.rejected, updateEnrollmentInfo.rejected, addEnrollmentInfo.rejected, unenrollCourse.rejected];
        pendingStates.forEach(pendingState => {
            builder.addCase(pendingState, (state) => {
                state.loading = true;
            });
        });
        rejectedStates.forEach(rejectedState => {
            builder.addCase(rejectedState, (state, action) => {
                state.loading = false;
                state.error = (action.payload as ITranportResponse).error.message;
            });
        });
        builder.addCase(fetchEnrollmentsList.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.enrollments = payload;
        });
        builder.addCase(addEnrollmentInfo.fulfilled, (state, { payload }) => {
            state.loading = false;
        });
        builder.addCase(updateEnrollmentInfo.fulfilled, (state) => {
            state.loading = false;
        });
        builder.addCase(unenrollCourse.fulfilled, (state, {payload}) => {
            state.loading = false;
            state.enrollments = state.enrollments.filter(v => v.id === payload)
        });
    },
});

export const { resetEnrollments } = enrollmentsSlice.actions;

export default enrollmentsSlice.reducer;