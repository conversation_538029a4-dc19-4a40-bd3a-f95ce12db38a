import { RouteTransport } from "./routeTransport";
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';
const ROOT_PATH = API_BASE_URL + "/api/";
type REQUEST_METHOD = 'GET' | 'POST' | 'PUT' | 'DELETE';

class Transport extends RouteTransport{
    protected async fetchWrapper(url: string, method: REQUEST_METHOD, body = '', customHeaders: any = {}): Promise<any> {
        url = url.indexOf('http') === -1 ? `${ROOT_PATH}${url}` : url
        const headers = {
            "Content-Type": "application/json",
            ...customHeaders
        }
        if (this.accessToken) {
            headers.Authorization = this.accessToken
        }
        try {
            const reqObj: RequestInit = {method, headers, credentials: 'include'};
            if (body) {
                reqObj.body = body;
            }
            const response = await fetch(url, reqObj);
            const status = response.status
            const contentType = response.headers.get('Content-Type');
            let data;
            if (status === 504) {
                return Promise.reject({
                    isError: true,
                    error: {
                        message: "Looks like the server is taking too long to respond. This might be due to downtime on the provider's side. Please try again in a while!"
                    },
                    statusCode: status
                })
            }
            if(contentType === 'text/plain;charset=UTF-8') {
                const content = await response.text();
                data = {content}
            } else {
                data = await response.json();
            }
            if (status === 200) {
                return {data};
            } else {
                return Promise.reject({
                    isError: true,
                    error: data,
                    statusCode: status
                })
            }
        } catch(e) {
            return Promise.reject({
                isError: true,
                error: e,
                statusCode: 500
            })
        }
    }
}

export const transport = new Transport();