import { IProfileForm } from "../../types/app";

let currentUser: IProfileForm | null = null;
export const GA_TRACKING_ID = process.env.NODE_ENV === "production" ? "G-L742X5NRGJ" : "G-FS1GME4DVF";

export const pageview = (url: string, user: IProfileForm | null): void => {
    currentUser = user;
    if (!window?.gtag) { return; }
    window.gtag("config", GA_TRACKING_ID, {
        page_path: url,
        isGuest: !currentUser,
        userId: currentUser?.email
    });
};
type GTAG_ACTION = "btn_click" | "link_click" | "api_response"
type GTAG_EVENT_CATEGORY = "click" | "api";
type GTagEvent = {
  action: GTAG_ACTION;
  category: GTAG_EVENT_CATEGORY;
  label: string;
  value: any;
};

export const gaCustomEvent = ({ action, category, label, value }: GTagEvent): void => {
    if (!window?.gtag) { return; }
    window.gtag("event", action, {
        event_category: category,
        event_label: label,
        additional_info: JSON.stringify({
            isGuest: !currentUser,
            userId: currentUser?.user_id,
            value
        }),
    });
};