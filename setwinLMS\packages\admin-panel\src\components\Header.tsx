"use client";
import ThemeSwitcher, { ThemeToggle } from "./ThemeSwitcher";
import Link from "next/link";
import Image from "next/image";
import { Avatar, useMediaQuery, useTheme } from "@mui/material";
import { AuthButton } from "./auth/AuthButton";
import { useAppDispatch, useAppSelector } from "../lib/hooks";
import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { deepOrange } from "@mui/material/colors";
import { PrimaryButton } from "./ui/buttons";
import { handleLogout } from "../lib/features/auth/authSlice";
import { Logout } from "@mui/icons-material";
import { Bell, Search } from 'lucide-react';
type SHOW_LINKS = 'YES' | 'NO' | 'IS_LOGGED_IN';
interface IPageLinks {
    label: string;
    href: string;
    show: SHOW_LINKS
}
const pageLinks: IPageLinks[] = [
    {
        label: "Overview",
        href: `/`,
        show: 'YES',
    },
    {
        label: "My Courses",
        href: `/courses`,
        show: 'IS_LOGGED_IN',
    }
];
export function Header() {
  const isLoggedIn = useAppSelector(state => state.authState.isLoggedIn);
  const userProfile = useAppSelector(state => state.userState.userProfile);
  const theme = useTheme();
  const pathName = usePathname();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const mobileView = useMediaQuery(theme.breakpoints.down('sm'));
  const [activeIndex, setActiveIndex] = useState(0);
  const filteredPageLinks = pageLinks.filter(v => v.show === 'YES' || (v.show === 'IS_LOGGED_IN' && isLoggedIn));
  const toggleClassName = (index: number) => {
    setActiveIndex(index);
  };
  useEffect(() => {
    const index = pathName === "/" ? 0 : filteredPageLinks.findIndex(v => v.href !== "/" && pathName.includes(v.href));
    setActiveIndex(index);
  }, [pathName, filteredPageLinks]);
  const oldHeader = () => {
    return (
      <div className="w-full">
          <nav className="w-full flex border-b border-b-foreground/10 h-16 px-8 py-4 justify-between">
            <div className="flex flex-row gap-2">
              <Link href="/" className="relative flex justify-center">
                <Image
                    src="/logos/setwin.png"
                    style={{
                        marginTop: mobileView ? "18px" : "0px"
                    }}
                    alt="Setin logo"
                    width={mobileView ? 90 : 110}
                    height={mobileView ? 90 : 110}
                    className="rounded-full"
                />
              </Link>
              <div className="flex flex-row justify-around">
                {filteredPageLinks.filter(v => v.show).map(({ href, label }, idx) => (
                    <Link href={href} key={idx} onClick={() => toggleClassName(idx)} className="z-10">
                      <div className={`px-5 py-2 text-xs uppercase outline-none border-custom-border-200
                          hover:bg-custom-background-100
                          text-center font-normal hover:text-custom-text-100 ${activeIndex === idx ? 'underline underline-offset-8 text-custom-text-100' : 'text-custom-text-100'}`}>
                          {label}
                      </div>
                    </Link>
                ))}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ThemeSwitcher />
              <AuthButton showMenu={true} />
            </div>
          </nav>
      </div>
    );
  }
  const logout = async () => {
    dispatch(handleLogout())
    router.push("/");
  }
  return (
    <header className="flex h-16 items-center justify-between border-b bg-white px-4 dark:border-gray-800 dark:bg-gray-900">
      <div className="flex items-center">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="h-10 rounded-lg border bg-gray-50 pl-10 pr-4 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
          />
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <ThemeToggle />
        <button className="relative rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-800">
          <Bell className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          <span className="absolute right-1 top-1 h-2 w-2 rounded-full bg-red-500"></span>
        </button>
        <div className="flex items-center gap-2">
          {userProfile?.avatar_url ? <img
            src={userProfile.avatar_url}
            alt={userProfile?.first_name + " " + userProfile?.last_name}
            className="h-8 w-8 rounded-full"
          /> : <Avatar sx={{ bgcolor: deepOrange[500] }}>{userProfile?.first_name?.charAt(0)}</Avatar>}
          <div className="hidden text-sm sm:block">
            <p className="font-medium text-gray-700 dark:text-gray-300">{userProfile?.first_name + " " + userProfile?.last_name}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">{userProfile.role.toLocaleUpperCase()}</p>
          </div>
        </div>
        <PrimaryButton
          onClick={logout}
          className="rounded-lg p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Logout className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        </PrimaryButton>
      </div>
    </header>
  );
}
