// context/AuthContext.js
"use client"
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAppSelector } from '../lib/hooks';

export const AuthContextProvider = ({ children }: { children: React.ReactNode}) => {
    const { isLoggedIn, loading } = useAppSelector(state => state.authState);
    const router  = useRouter();
    useEffect(() => {
        if (!isLoggedIn && !loading) {
          router.push('/login');
        }
    }, []);
    // Protected routes check authentication
    return isLoggedIn ? <>{children}</> : null;
};