"use client"
import { <PERSON>, Cpu, Network, Sparkles } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
const features = [
    {
      icon: Brain,
      title: 'AI Fundamentals',
      description: 'Master the core concepts of artificial intelligence and machine learning'
    },
    {
      icon: Network,
      title: 'Deep Learning',
      description: 'Dive deep into neural networks and advanced AI architectures'
    },
    {
      icon: Cpu,
      title: 'Machine Learning',
      description: 'Learn practical ML algorithms and their real-world applications'
    },
    {
      icon: Spark<PERSON>,
      title: 'Generative AI',
      description: 'Explore the cutting-edge world of generative models and LLMs'
    }
];

export const LandingPage = () => {
    const router = useRouter();
    return (
        <div className="min-h-screen">
          {/* Hero Section */}
          <div className="relative overflow-hidden bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-24 sm:px-8 md:px-12">
            <div className="relative mx-auto max-w-7xl">
              <div className="lg:grid lg:grid-cols-12 lg:gap-8">
                <div className="sm:text-center md:mx-auto md:max-w-2xl lg:col-span-6 lg:text-left">
                  <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl">
                    Welcome to <span className="text-indigo-200">AI4ge</span>
                  </h1>
                  <p className="mt-6 text-xl text-indigo-100">
                    Forge your future in AI with comprehensive courses taught by industry experts. Master the technologies shaping tomorrow.
                  </p>
                  <div className="mt-10 flex gap-4 sm:justify-center lg:justify-start">
                    <button
                      onClick={() => router.push('/courses')}
                      className="rounded-lg bg-white px-8 py-3 text-lg font-semibold text-indigo-600 shadow-sm hover:bg-indigo-50"
                    >
                      Start Learning
                    </button>
                    {/* <button
                      onClick={() => router.push('/dashboard')}
                      className="rounded-lg border border-white bg-transparent px-8 py-3 text-lg font-semibold text-white hover:bg-white/10"
                    >
                      View Dashboard
                    </button> */}
                  </div>
                </div>
                <div className="relative mt-12 sm:mx-auto sm:max-w-lg lg:col-span-6 lg:mx-0 lg:mt-0 lg:flex lg:max-w-none lg:items-center">
                    <Image
                        src="/site-photos/landing.avif"
                        alt="AI Learning Platform"
                        className="w-full rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 lg:w-auto"
                        width="500"
                        height="300"
                    />
                </div>
              </div>
            </div>
          </div>
    
          {/* Features Grid */}
          <div className="mx-auto max-w-7xl px-6 py-24 sm:px-8 md:px-12 dark:text-slate-100">
            <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4 cursor-pointer" onClick={() => router.push("/dashboard")}>
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div
                    key={index}
                    className="group rounded-xl bg-white p-8 shadow-sm transition-all hover:shadow-lg dark:bg-gray-800"
                    onClick={() => router.push('/dashboard')}
                  >
                    <div className="mb-4 inline-block rounded-lg bg-indigo-100 p-3 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
                      <Icon className="h-6 w-6" />
                    </div>
                    <h3 className="mb-2 text-xl font-semibold text-white">{feature.title}</h3>
                    <p className="text-gray-600 dark:text-gray-400">{feature.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
    );
}