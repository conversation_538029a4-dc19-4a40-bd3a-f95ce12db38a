import { Button, ButtonProps } from "@mui/material";

export const PrimaryButton: React.FC<ButtonProps> = ({
  children,
  className = "",
  onClick,
  type = "button",
  disabled = false,
  size = "md",
  variant = 'contained',
  startIcon,
}) => (
  <Button
    type={type}
    className={`${className} uppercase font-medium duration-300 ${
      size === "sm"
        ? "rounded px-3 py-2 text-xs"
        : size === "md"
        ? "rounded-md px-3.5 py-2 text-sm"
        : "rounded-lg px-4 py-2 text-base"
    } ${disabled ? "cursor-not-allowed opacity-70 hover:opacity-70" : ""}}`}
    onClick={onClick}
    sx={{
      color: "rgb(26, 75, 91)",
      backgroundColor: "white",
      borderColor:"rgb(26, 75, 91)",
      '&:hover': {
        backgroundColor: "white"
      }
    }}
    startIcon={startIcon}
    disabled={disabled}
    variant={variant}
  >
    {children}
  </Button>
);
