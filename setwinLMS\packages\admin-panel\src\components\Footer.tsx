
import { Facebook, Instagram, LinkedIn } from "@mui/icons-material";
import FavoriteRoundedIcon from '@mui/icons-material/FavoriteRounded';

const socialLinks = [
    {
      title: 'Instagram',
      href: 'https://www.instagram.com/bhavesh_kankaria/',
      icon: Instagram
    },
    {
      title: 'Facebook',
      href: 'https://www.facebook.com/dbkjain',
      icon: Facebook
    },
    {
      title: 'LinkedIn',
      href: 'https://www.linkedin.com/in/bhavesh-kumar-b88013160/',
      icon: LinkedIn
    }
];
export const Footer = () => {
    return <footer className="h-20 items-center justify-center bg-custom-sidebar-background-100 px-8 w-full">
        <section className="sm:px-8 py-4 border-b border-custom-border-200 flex justify-center">
            <div className="flex flex-row items-center">
                <ul className="flex flex-row justify-center items-center" role="list">
                    {socialLinks.map((link, idx) => <li key={idx}>
                    <a
                        href={link.href}
                        className="px-2 sm:px-4"
                        target="_blank"
                        rel="noopener noreferrer"
                        title={"Follow us on " + link.title}
                    >
                        <link.icon className="h-8 w-8"/>
                    </a>
                    </li>)}
                </ul>
            </div>
        </section>
        <div className="copyright text-center sm:px-8 py-2">
        <h3 className="text-lg">
          Build with{" "} 
          <FavoriteRoundedIcon className="text-red-700"/>
          {" "}by Setwin Team
        </h3>
        </div>
    </footer>
}