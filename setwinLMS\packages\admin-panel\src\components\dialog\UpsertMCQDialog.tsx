import { Dialog, DialogActions, DialogContent, DialogTitle, DialogContentText } from "@mui/material";
import { SecondaryButton } from "../ui/buttons/secondary-button";
import { PrimaryButton } from "../ui/buttons";
import { MCQComponent } from "../ui/MCQ";
import { IMCQContent } from "../../types/app";

interface UpsertMCQDialogProps {
    open: boolean;
    existingData?: IMCQContent;
    onClose: () => void;
    onSubmit: (mcqData: IMCQContent) => void;
}
export const UpsertMCQDialog:React.FC<UpsertMCQDialogProps> = ({ open, existingData, onClose, onSubmit}) => {
    return (
        <Dialog open={open} onClose={onClose}>
            <DialogTitle>Add a question for your quiz?</DialogTitle>
            <DialogContent sx={{minWidth: "500px"}}>
                <MCQComponent 
                    existingData={existingData}
                    onClose={onClose}
                    onSubmit={onSubmit}
                />
            </DialogContent>
        </Dialog>
    );
}