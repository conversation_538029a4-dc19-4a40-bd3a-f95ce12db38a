"use client"
import { useEffect } from "react";
import { Footer } from "../../components/Footer";
import { Header } from "../../components/Header";
import { Sidebar } from "../../components/Sidebar";
import { CurrencyProvider } from "../../context/CurrencyContext";
import { SettingsProvider } from "../../context/SettingsContext";
import { useAppDispatch, useAppSelector } from "../../lib/hooks";
import { isUserLoggedIn } from "../../lib/features/auth/authSlice";
import { unwrapResult } from "@reduxjs/toolkit";
import { fetchCurrentUser } from "../../lib/features/currentUser/currentUserSlice";
import { fetchEnrollmentsList } from "../../lib/features/courses/enrollmentSlice";
import { AuthContextProvider } from "../../context/AuthContextProvider";

const ProtectedRouteLayout: React.FC<{children: React.ReactNode}> = ({ children }: { children: React.ReactNode }) => {
    const loading = useAppSelector(state => state.authState.loading)
    const dispatch = useAppDispatch();
    useEffect(() => {
        dispatch(fetchCurrentUser());
        dispatch(fetchEnrollmentsList());
    }, [dispatch]);
    return (
        <AuthContextProvider>
        <CurrencyProvider>
            <SettingsProvider>
                <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
                    <Sidebar />
                    <div className="flex flex-1 flex-col overflow-hidden">
                        <Header />
                        <main className="flex-1 overflow-y-auto">
                            {children}
                        </main>
                    </div>
                </div>
            </SettingsProvider>
        </CurrencyProvider>
        </AuthContextProvider>
    );
}

export default ProtectedRouteLayout;
