"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ModuleList } from './ModuleList';
import { useAppDispatch, useAppSelector } from '../../lib/hooks';
import GrowlWrapper from '../notifications/GrowlWrapper';
import { deleteCourse, fetchCourseInfo, updateCourseInfo } from '../../lib/features/courses/courseSlice';
import { ICourse } from '../../types/app';
import { ConfirmDialog } from '../dialog/DeleteDialog';
import { ArrowLeft, Clock, Users, BookOpen, AlertCircle, LinkIcon, ExternalLink, Upload } from 'lucide-react';
import { useCurrency } from '../../context/CurrencyContext';
import { addEnrollmentInfo } from '../../lib/features/courses/enrollmentSlice';
import { ModuleListReadOnly } from './ModuleListReadOnly';

interface CourseDetailViewProps {
    cid: string;
    ismanage: boolean;
}

export const CourseDetailView:React.FC<CourseDetailViewProps> = ({ cid, ismanage }) => {
  const [isPublishModalOpen, setPublishModalOpen] = useState(false);
  const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const {notify} = GrowlWrapper();
  useEffect(() => {
    if (cid) {
      // Fetch course details and modules
      dispatch(fetchCourseInfo({courseId: cid as string}));
    }
  }, [cid]);
  const { userProfile } = useAppSelector(state => state.userState);
  const { course } = useAppSelector((state) => state.courseState);
  const { enrollments } = useAppSelector(state => state.enrollmentsState);
  const { formatPrice } = useCurrency();
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [enrollError, setEnrollError] = useState<string | null>(null);

  const isEnrolled = enrollments.find(v => v.student.user_id === userProfile.user_id)

  // Add custom styles for markdown content
  const markdownStyles = `
    .markdown-content {
      @apply prose prose-lg prose-indigo dark:prose-invert max-w-none;
    }
    .markdown-content h1 {
      @apply text-3xl font-bold mb-6;
    }
    .markdown-content h2 {
      @apply text-2xl font-semibold mt-8 mb-4;
    }
    .markdown-content h3 {
      @apply text-xl font-medium mt-6 mb-3;
    }
    .markdown-content p {
      @apply mb-4;
    }
    .markdown-content ul {
      @apply list-disc pl-6 mb-4;
    }
    .markdown-content ol {
      @apply list-decimal pl-6 mb-4;
    }
    .markdown-content li {
      @apply mb-2;
    }
    .markdown-content hr {
      @apply my-8;
    }
    .markdown-content blockquote {
      @apply border-l-4 border-indigo-500 pl-4 italic my-4;
    }
    .markdown-content code {
      @apply bg-gray-100 dark:bg-gray-800 rounded px-1;
    }
    .markdown-content pre {
      @apply bg-gray-100 dark:bg-gray-800 rounded p-4 my-4 overflow-x-auto;
    }
  `;

  // Add the styles to the document
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = markdownStyles;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  if (!course) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-lg text-gray-500">Course not found</p>
      </div>
    );
  }

  const handleEnroll = () => {
    if (!course) return;
    
    // Reset error state
    setEnrollError(null);
    setIsEnrolling(true);

    try {
      // Check if course is full
      if ((course.enrollments || []).length >= course.maxStudents) {
        throw new Error('This course is full');
      }

      // Check if student is already enrolled
      if (isEnrolled) {
        throw new Error('You are already enrolled in this course');
      }

      dispatch(addEnrollmentInfo({
        formData: {
          student: userProfile, 
          course: course,
        } as any
      }));
      router.push('/my-learning');
    } catch (error) {
      console.error('Failed to enroll:', error);
      setEnrollError(error instanceof Error ? error.message : 'Failed to enroll in course');
    } finally {
      setIsEnrolling(false);
    }
  };

  const handlePublishCourse = async () => {
    try {
      const status = course?.status === 'draft' ? 'published' : 'draft'
      dispatch(updateCourseInfo({ formData : {...course, status } as ICourse }));
      notify('Course published successfully!', 'success');
    } catch (error) {
      notify('Failed to publish course', 'error');
    } finally {
      setPublishModalOpen(false);
    }
  };

  const handleDeleteCourse = async () => {
    try {
      dispatch(deleteCourse({ courseId: cid as string }));
      notify('Course deleted successfully', 'success');
      router.push('/courses'); // Redirect to the courses list page
    } catch (error) {
      notify('Failed to delete course', 'error');
    }
  };

  const handleOpenPublishModal = () => setPublishModalOpen(true);
  const handleClosePublishModal = () => setPublishModalOpen(false);
  const handleOpenDeleteModal = () => setDeleteModalOpen(true);
  const handleCloseDeleteModal = () => setDeleteModalOpen(false);

  return (
    <div className="p-6 dark:text-slate-100">
      <div className="mb-6 flex items-center justify-between">
        {/* Back Button */}
        <button
          onClick={() => {
            router.push(ismanage ? '/manage-courses' : '/courses')
          }}
          className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800"
        >
          <ArrowLeft className="h-4 w-4" />
          {ismanage ? "Back to Manage Courses" : "Back to Courses"}
        </button>
        {!ismanage && userProfile.role === 'student' && <div className="flex gap-2">
          <button
            onClick={() => router.push(`/courses/${cid}/resume`)}
            className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            Resume Course
          </button>
        </div>}
        {ismanage && <div className="flex gap-2">
          <label className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700">
            <Upload className="h-5 w-5" />
            <span>Import Content</span>
            <input
              type="file"
              accept=".json,.csv"
              className="hidden"
              onChange={(e) => {
                // Handle file upload
                console.log('File upload:', e.target.files?.[0]);
              }}
            />
          </label>
          <button
            onClick={handleOpenPublishModal}
            className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800 bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            {course.status === 'draft' ? 'Publish Course' : 'Unpublish Course'}
          </button>
          <button
            onClick={handleOpenDeleteModal}
            className="mb-6 flex items-center gap-2 rounded-lg border px-4 py-2 bg-red-600 border-red-600 text-slate-100"
          >
            Delete Course
          </button>
        </div>}
      </div>
      {/* Course Header */}
      <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 p-8">
        <div className="absolute inset-0">
          <img
            src={course.thumbnail}
            alt={course.title}
            className="h-full w-full object-cover opacity-20"
          />
        </div>
        <div className="relative">
          <div className="flex items-center gap-3">
            <h1 className="mb-2 text-3xl font-bold text-white">{course.title}</h1>
            {course.isExternal && (
              <LinkIcon className="h-6 w-6 text-white" />
            )}
          </div>
          <p className="text-lg text-indigo-100">{course.description}</p>
          
          <div className="mt-4 flex gap-4">
            <div className="flex items-center gap-2 text-indigo-100">
              <Clock className="h-5 w-5" />
              <span>{course.duration}</span>
            </div>
            <div className="flex items-center gap-2 text-indigo-100">
              <Users className="h-5 w-5" />
              <span>{(course.enrollments || []).length}/{course.maxStudents} enrolled</span>
            </div>
            {!course.isExternal && course.modules && (
              <div className="flex items-center gap-2 text-indigo-100">
                <BookOpen className="h-5 w-5" />
                <span>{course.modules.length} modules</span>
              </div>
            )}
          </div>

          <div className="mt-6 space-y-2">
            {enrollError && (
              <div className="flex items-center gap-2 rounded-lg bg-red-100 px-4 py-2 text-sm text-red-700 dark:bg-red-900/50 dark:text-red-400">
                <AlertCircle className="h-4 w-4" />
                {enrollError}
              </div>
            )}
            {!isEnrolled && userProfile?.role === 'student' && (
              <button
                onClick={handleEnroll}
                disabled={isEnrolling || (course.enrollments?.length || 0) >= course.maxStudents}
                className="rounded-lg bg-white px-6 py-2 font-semibold text-indigo-600 hover:bg-indigo-50 disabled:bg-gray-300 disabled:text-gray-600"
              >
                {isEnrolling ? 'Enrolling...' : 'Enroll Now'}
              </button>
            )}
            {course.isExternal && (
              <a
                href={course.externalUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 rounded-lg bg-green-600 px-6 py-2 font-semibold text-white hover:bg-green-700"
              >
                <ExternalLink className="h-4 w-4" />
                Access Course
              </a>
            )}
          </div>
        </div>
      </div>
      {ismanage && <ModuleList courseId={cid as string} />}
      {/* Course Content */}
      {!ismanage && !course.isExternal && <ModuleListReadOnly courseId={cid as string} /> }
      <ConfirmDialog
        open={isPublishModalOpen} 
        onClose={handleClosePublishModal} 
        onConfirm={handlePublishCourse} 
        dialogTitle={course.status === 'draft' ? "Publish Course" : "Unpublish Course"}
        dialogContent={'Are you sure you want to' + (course.status === 'draft' ? ' publish ' : ' unpublish ') + 'this course?'}
        submitText={course.status === 'draft' ? 'Yes, Publish' : 'Yes, Unpublish'}
      />

      {/* Modal for deleting */}
      <ConfirmDialog
        open={isDeleteModalOpen} 
        onClose={handleCloseDeleteModal} 
        onConfirm={handleDeleteCourse} 
        dialogTitle="Delete Course" 
      />
    </div>
  );
}