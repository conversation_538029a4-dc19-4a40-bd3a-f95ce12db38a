import { combineSlices, configureStore } from '@reduxjs/toolkit'
import { currentUserSlice } from '../lib/features/currentUser/currentUserSlice'
import { authSlice } from '../lib/features/auth/authSlice';
import { notificationSlice } from '../lib/features/notifications/notificationSlice';
import { coursesSlice } from './features/courses/coursesSlice';
import { courseSlice } from './features/courses/courseSlice';
import { modulesSlice } from './features/courses/modulesSlice';
import { moduleSlice } from './features/courses/moduleSlice';
import { lessonSlice } from './features/courses/lessonSlice';
import { lessonsSlice } from './features/courses/lessonsSlice';
import { enrollmentsSlice } from './features/courses/enrollmentSlice';

const rootReducer = combineSlices(currentUserSlice, authSlice, notificationSlice, coursesSlice, courseSlice, modulesSlice, lessonsSlice, lessonSlice, moduleSlice, enrollmentsSlice);
export type RootState = ReturnType<typeof rootReducer>;

export const makeStore = () => {
  return configureStore({
    reducer: rootReducer
  })
}

// Infer the type of makeStore
export type AppStore = ReturnType<typeof makeStore>
export type AppDispatch = AppStore['dispatch']