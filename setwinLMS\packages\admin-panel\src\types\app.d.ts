import { AlertColor } from "@mui/material";

export type USER_ROLE = 'student' | 'instructor' | 'admin';
export type ICourseStatus = 'draft' | 'published' | 'archived';
export type ILessonType = 'text' | 'quiz' | 'video' | 'discussion';
export type ENROLLMENT_STATUS = 'active' | 'completed' | 'dropped' | 'waitlisted';
export interface IUser{
  email?: string;
}

export interface INotification {
  id?: string;
  duration?: number;
  message: string;
  severity: AlertColor;
}

export interface ILoginForm {
  email: string;
  password: string;
}

export interface ISignupForm extends ILoginForm, IProfileForm {
  confirmPassword: string;
}

export interface IProfileForm extends IUser{
  user_id?: string;
  first_name: string;
  last_name: string;
  role: USER_ROLE;
  phoneNumber?: string;
  avatar_url?: string;
  settings?: IProfileSettings;
}

export interface IProfileSettings {
  notifications: {
    emailnotifications: boolean;
    pushnotifications: boolean;
    courseupdates: boolean;
  };
  preferences: {
    wishlistEnabled: boolean;
    reminders: boolean;
  };
  dashboard: {
    showProgress: boolean;
    showUpcoming: boolean;
  };
}
export interface ITranportResponse {
  isError?: boolean;
  error?: any;
  statusCode?: number;
  data?: any;
}
export interface ICourseReview {
  id: string;
  rating: number;
  comment: string;
  created_at: Date;
  updated_at: Date;
  course: ICourse;
  reviewer: IUser;
}
export interface IEnrollment {
  id: string;
  created_at: Date;
  updated_at: Date;
  course: ICourse;
  student: IProfileForm;
  progress: string;
  status: ENROLLMENT_STATUS,
  lastAccessedModule: string;
  lastAccessedLesson: string;
  completedLessons: string[];
  
}
export interface IModule {
  id?: string;
  title: string;
  description: string;
  created_at?: Date;
  updated_at?: Date;
  position: number;
  course: ICourse;
  duration: number;
  lessons: ILesson[];
}
export interface ILesson {
  id?: string;
  title: string;
  content: string;
  position: number;
  lesson_type: ILessonType;
  video_url?: string;
  duration?: number;
  created_at?: Date;
  updated_at?: Date;
  module?: IModule;
}
export interface ICourse { 
  id?: string;
  title: string;
  description: string;
  price: number;
  image_url?: string;
  category?: string;
  status: ICourseStatus;
  created_at?: Date;
  updated_at?: Date;
  maxStudents: number;
  enrollments?: IEnrollment[];
  modules?: IModule[];
  reviews?: ICourseReview[];
  duration: number;
  instructor: IProfileForm;
  thumbnail: string;
  objectives: string;
  structure: string;
  isExternal?: boolean;
  externalUrl?: string;
}

export interface IMCQContent {
  id: number;
  question: string;
  options: string[];
  answer: string;
}