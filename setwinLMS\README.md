# SetwinLMS - Learning Management System

![Setwin Admin Panel](https://img.shields.io/badge/Setwin-Admin%20Portal-blue)
![Next.js](https://img.shields.io/badge/Next.js-14.2.5-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5.6.2-blue)
![React](https://img.shields.io/badge/React-18.3.1-blue)
![Material-UI](https://img.shields.io/badge/Material--UI-5.12.3-blue)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.1.6-blue)

## 📋 Overview

SetwinLMS is a comprehensive Learning Management System designed to provide an intuitive admin portal for configuring courses that students can enroll in. The platform offers a modern, responsive interface built with cutting-edge web technologies.

## 🚀 Features

### 📚 Course Management
- **Course Creation & Editing**: Create and manage comprehensive courses with detailed descriptions
- **Module Organization**: Structure courses into organized modules for better learning flow
- **Lesson Management**: Add, edit, and organize lessons within modules
- **Content Editor**: Rich text editor powered by TipTap for creating engaging content
- **Drag & Drop**: Reorder courses, modules, and lessons with React Beautiful DnD

### 👥 User Management
- **Authentication System**: Secure login and signup functionality
- **Role-based Access**: Support for different user roles (admin, student)
- **User Profiles**: Comprehensive user profile management
- **SSO Integration**: Single Sign-On support for seamless authentication

### 📊 Analytics & Dashboard
- **Interactive Dashboard**: Real-time analytics with Recharts visualization
- **Student Enrollment Tracking**: Monitor course enrollments and student progress
- **Performance Metrics**: Track course completion rates and user engagement
- **Data Visualization**: Beautiful charts and graphs for insights

### 🎨 User Experience
- **Modern UI**: Clean, intuitive interface built with Material-UI components
- **Dark/Light Theme**: Theme switching with next-themes
- **Responsive Design**: Mobile-first design with TailwindCSS
- **Real-time Notifications**: Toast notifications for user feedback
- **Progress Tracking**: Visual progress indicators with NProgress

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14.2.5 (React 18.3.1)
- **Language**: TypeScript 5.6.2
- **Styling**: TailwindCSS 3.1.6 + Material-UI 5.12.3
- **State Management**: Redux Toolkit 2.3.0
- **Rich Text Editor**: TipTap 2.0.4 with mui-tiptap
- **Charts**: Recharts 2.12.2
- **Drag & Drop**: React Beautiful DnD 13.1.1

### Development Tools
- **Monorepo**: Lerna 8.1.9 for package management
- **Build Tool**: Next.js with SWC minification
- **Linting**: ESLint with Next.js configuration
- **Type Checking**: TypeScript with strict mode
- **Styling**: PostCSS with Autoprefixer

### Analytics & Monitoring
- **Analytics**: Vercel Analytics & Speed Insights
- **SEO**: Next.js built-in SEO optimization
- **Performance**: Google Analytics integration

## 📁 Project Structure

```
setwinLMS/
├── packages/
│   └── admin-panel/           # Main admin panel application
│       ├── src/
│       │   ├── app/           # Next.js App Router pages
│       │   │   ├── (protected)/  # Protected routes
│       │   │   ├── api/       # API routes
│       │   │   ├── login/     # Authentication pages
│       │   │   └── signup/
│       │   ├── components/    # React components
│       │   │   ├── auth/      # Authentication components
│       │   │   ├── courses/   # Course management
│       │   │   ├── dashboard/ # Dashboard components
│       │   │   ├── ui/        # Reusable UI components
│       │   │   └── ...
│       │   ├── lib/           # Utilities and configurations
│       │   │   ├── features/  # Redux slices
│       │   │   ├── network/   # API transport layer
│       │   │   └── utils/     # Helper functions
│       │   ├── styles/        # Global styles
│       │   ├── types/         # TypeScript type definitions
│       │   └── constants/     # Application constants
│       ├── public/            # Static assets
│       └── package.json
├── package.json               # Root package configuration
├── lerna.json                # Lerna configuration
└── tsconfig.base.json        # Base TypeScript configuration
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SetwinLMS/setwinLMS
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the admin-panel package:
   ```env
   NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
   # Add other environment variables as needed
   ```

### Development

1. **Start the development server**
   ```bash
   # Start admin panel only
   npm run dev:admin
   
   # Start all services
   npm run dev
   ```

2. **Access the application**
   - Admin Panel: http://localhost:3000
   - API: http://localhost:3001 (if backend is running)

### Building for Production

```bash
# Build admin panel
npm run build:admin

# Build all packages
npm run build
```

### Running in Production

```bash
# Start admin panel
npm run start:admin

# Start with Docker
npm run start:docker
```

## 📜 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev:admin` | Start admin panel in development mode |
| `npm run build:admin` | Build admin panel for production |
| `npm run start:admin` | Start admin panel in production mode |
| `npm run dev:backend` | Start backend in development mode |
| `npm run dev` | Start all services in development mode |
| `npm run build` | Build all packages |
| `npm run lint` | Run ESLint on all packages |
| `npm run test` | Run tests on all packages |
| `npm run lint:fix` | Fix ESLint issues automatically |

## 🔧 Configuration

### API Configuration
The application connects to a backend API. Configure the API base URL in your environment variables:

```env
NEXT_PUBLIC_API_BASE_URL=your-api-url
```

### Theme Configuration
The application supports light and dark themes. Theme configuration can be found in:
- `src/styles/globals.css` - CSS custom properties
- `tailwind.config.js` - TailwindCSS theme extension

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙋‍♂️ Support

For support and questions:
- Create an issue in the repository
- Contact the Setwin Team
- Visit: https://setwin.in

## 🔗 Links

- **Website**: https://setwin.in
- **Documentation**: [Coming Soon]
- **API Documentation**: [Coming Soon]

---

**Built with ❤️ by the Setwin Team**
