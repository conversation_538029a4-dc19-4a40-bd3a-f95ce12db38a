import React, { useEffect, useState } from "react";
import { ICourse, IModule } from "../../types/app";
import { RichTextEditor } from "../ui/RichTextEditor";
import CourseStructure from "./CourseStructure";

interface IResumeCenterPanelProps {
    lessonId: string;
    moduleId: string;
    course: ICourse;
    modules: IModule[];
}
export const ResumeCenterPanel: React.FC<IResumeCenterPanelProps> = ({ lessonId, moduleId, modules, course }) => {
    const [content, setContent] = useState('');
    useEffect(() => {
        const getHTML = () => {
            if (moduleId === 'structure' && course.structure) {
                setContent(course.structure);
                return;
            }
            if (moduleId === 'objectives') {
                setContent(course.objectives);
                return;
            }
            const activeModule = modules.find(v => v.id === moduleId);
            if (!lessonId) {
                setContent(activeModule?.description as string)
                return;
            }
    
            const activeLesson = activeModule?.lessons.find(v => v.id === lessonId);
            setContent(activeLesson?.content as string)
            return;
        }
        getHTML();
    }, [course, moduleId, lessonId, modules]);

    return (moduleId === 'structure' && !course.structure ? <CourseStructure /> : <RichTextEditor readonly={true} inputHtml={content} />)

}